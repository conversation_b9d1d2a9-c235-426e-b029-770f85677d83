/*******************************************************************************
 * 文件名: uart_simple.c
 * 描述: SH79F084B 最简单的UART测试程序
 * 功能: 持续发送0x55字节
 *******************************************************************************/

#include <SH79F084B.H>

// 二进制宏定义
#define bin(n) ((((0x##n##L>>21)&0x80)|(((0x##n##L)>>18)&0x40)|(((0x##n##L)>>15)&0x20)|(((0x##n##L)>>12)&0x10)|(((0x##n##L)>>9)&0x08)|(((0x##n##L)>>6)&0x04)|(((0x##n##L)>>3)&0x02)|((0x##n##L)&0x01)))

/*******************************************************************************
 * 函数名: main
 * 描述: 主函数 - 最简化的UART发送0x55程序
 *******************************************************************************/
void main(void)
{
    unsigned int delay;
    
    // === 系统基本初始化 ===
    EA = 0;                         // 关闭中断
    RSTSTAT &= 0xF8;               // 清看门狗
    
    // === GPIO配置 ===
    // P3.0=RXD, P3.1=TXD 配置（参考原始UartDriver.c）
    P3 |= bin(10001011);           // P3端口初始化
    P3M0 &= bin(01110100);         // P3.0(RXD)、P3.1(TXD)配置
    P3M1 &= bin(01110100);         // 配置为准双向模式
    
    // === 定时器2配置（波特率发生器）===
    // 9600波特率计算: 65536 - 11059200/16/9600 = 65536 - 72 = 65464 = 0xFF98
    TL2 = 0x98;                    // 定时器2低字节
    TH2 = 0xFF;                    // 定时器2高字节
    RCAP2L = 0x98;                 // 重载值低字节
    RCAP2H = 0xFF;                 // 重载值高字节
    T2CON = bin(00110000);         // 定时器2作为UART时钟，自动重载
    T2MOD = bin(10000000);         // 系统时钟作为定时器2时钟源
    TR2 = 1;                       // 启动定时器2
    
    // === UART配置 ===
    SCON = bin(01010000);          // 模式1: 8位数据，可变波特率，允许接收
    PCON &= bin(01111111);         // 清除SMOD位
    TI = 0;                        // 清除发送标志
    RI = 0;                        // 清除接收标志
    
    // === 主循环：持续发送0x55 ===
    while(1)
    {
        RSTSTAT &= 0xF8;           // 清看门狗
        
        SBUF = 0x55;               // 发送0x55
        while(TI == 0);            // 等待发送完成
        TI = 0;                    // 清除发送标志
        
        // 简单延时
        for(delay = 0; delay < 30000; delay++);
    }
}

/*******************************************************************************
 * 使用说明:
 * 
 * 1. 硬件连接:
 *    - P3.1(TXD) -> 串口工具RX
 *    - P3.0(RXD) -> 串口工具TX (可选，本程序只发送)
 *    - GND -> 串口工具GND
 * 
 * 2. 串口工具设置:
 *    - 波特率: 9600
 *    - 数据位: 8位
 *    - 停止位: 1位  
 *    - 奇偶校验: 无
 * 
 * 3. 预期现象:
 *    - 串口工具持续收到0x55字节
 *    - 十六进制显示: 55 55 55 55 ...
 *    - ASCII显示: U U U U ... (0x55对应字符'U')
 * 
 * 4. 波特率计算表 (11.0592MHz晶振):
 *    - 1200:   TL2=0x00, TH2=0xE8, RCAP2L=0x00, RCAP2H=0xE8
 *    - 2400:   TL2=0x00, TH2=0xF4, RCAP2L=0x00, RCAP2H=0xF4
 *    - 4800:   TL2=0x80, TH2=0xFA, RCAP2L=0x80, RCAP2H=0xFA
 *    - 9600:   TL2=0x98, TH2=0xFF, RCAP2L=0x98, RCAP2H=0xFF (当前使用)
 *    - 19200:  TL2=0xCC, TH2=0xFF, RCAP2L=0xCC, RCAP2H=0xFF
 *    - 38400:  TL2=0xE6, TH2=0xFF, RCAP2L=0xE6, RCAP2H=0xFF
 *
 * 5. 故障排除:
 *    - 如果没有数据: 检查硬件连接和波特率设置
 *    - 如果数据错误: 检查晶振频率是否为11.0592MHz
 *    - 如果波特率不对: 使用上面的波特率计算表
 *******************************************************************************/
