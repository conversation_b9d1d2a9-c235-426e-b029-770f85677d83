LX51 LINKER/LOCATER V4.66.93.0                                                          06/03/2025  11:44:05  PAGE 1


LX51 LINKER/LOCATER V4.66.93.0, INVOKED BY:
C:\KEIL_V5\C51\BIN\LX51.EXE STARTUP.obj, main.obj, interrupt.obj, UartDriver.obj TO SH79F083_UART


CPU MODE:     8051 MODE
MEMORY MODEL: SMALL


INPUT MODULES INCLUDED:
  STARTUP.obj (?C_STARTUP)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  main.obj (MAIN)
         COMMENT TYPE 0: C51 V9.59.0.0
  interrupt.obj (INTERRUPT)
         COMMENT TYPE 0: C51 V9.59.0.0
  UartDriver.obj (UARTDRIVER)
         COMMENT TYPE 0: C51 V9.59.0.0
  C:\KEIL_V5\C51\LIB\C51S.LIB (?C_INIT)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL_V5\C51\LIB\C51S.LIB (?C?CLDOPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL_V5\C51\LIB\C51S.LIB (?C?CSTPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL_V5\C51\LIB\C51S.LIB (?C?SLDIV)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL_V5\C51\LIB\C51S.LIB (?C?ULCMP)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL_V5\C51\LIB\C51S.LIB (?C?ULDIV)
         COMMENT TYPE 1: A51 / ASM51 Assembler


ACTIVE MEMORY CLASSES OF MODULE:  SH79F083_UART (?C_STARTUP)

BASE        START       END         USED      MEMORY CLASS
==========================================================
C:000000H   C:000000H   C:00FFFFH   000814H   CODE
I:000000H   I:000000H   I:0000FFH   000001H   IDATA
I:000000H   I:000000H   I:00007FH   000036H   DATA
X:000000H   X:000000H   X:00FFFFH   00005CH   XDATA
I:000020H.0 I:000020H.0 I:00002FH.7 000001H.3 BIT


MEMORY MAP OF MODULE:  SH79F083_UART (?C_STARTUP)


START     STOP      LENGTH    ALIGN  RELOC    MEMORY CLASS   SEGMENT NAME
=========================================================================

* * * * * * * * * * *   D A T A   M E M O R Y   * * * * * * * * * * * * *
000000H   000007H   000008H   ---    AT..     DATA           "REG BANK 0"
000008H   00000CH   000005H   BYTE   UNIT     DATA           ?DT?_CALCHECKSUM?MAIN
00000DH   000011H   000005H   BYTE   UNIT     DATA           ?DT?_CALCHECKSUM1?INTERRUPT
000012H   000013H   000002H   BYTE   UNIT     DATA           ?DT?INTERRUPT
000014H   000014H   000001H   BYTE   UNIT     DATA           _DATA_GROUP_
000015H.0 00001FH.7 00000BH.0 ---    ---      **GAP**
000020H.0 000020H.5 000000H.6 BIT    UNIT     BIT            ?BI?INTERRUPT
000020H.6 000021H.2 000000H.5 BIT    UNIT     BIT            ?BI?MAIN
000021H.3 000021H   000000H.5 ---    ---      **GAP**
000022H   000042H   000021H   BYTE   UNIT     DATA           ?DT?MAIN
000043H   000043H   000001H   BYTE   UNIT     IDATA          ?STACK

* * * * * * * * * * *   C O D E   M E M O R Y   * * * * * * * * * * * * *
000000H   000002H   000003H   ---    OFFS..   CODE           ?CO??C_STARTUP?0
000003H   000005H   000003H   BYTE   OFFS..   CODE           ?INTERRUPT?00003
000006H   00000AH   000005H   BYTE   UNIT     CODE           ?PR?EX1_INT?INTERRUPT
00000BH   00000DH   000003H   BYTE   OFFS..   CODE           ?INTERRUPT?0000B
LX51 LINKER/LOCATER V4.66.93.0                                                        06/03/2025  11:44:05  PAGE 2


00000EH   000012H   000005H   BYTE   UNIT     CODE           ?PR?TIMER1_INT?INTERRUPT
000013H   000015H   000003H   BYTE   OFFS..   CODE           ?INTERRUPT?00013
000016H   00001AH   000005H   BYTE   UNIT     CODE           ?PR?TIMER2_INT?INTERRUPT
00001BH   00001DH   000003H   BYTE   OFFS..   CODE           ?INTERRUPT?0001B
00001EH   000022H   000005H   BYTE   UNIT     CODE           ?PR?UARTRECEIVEON?UARTDRIVER
000023H   000025H   000003H   BYTE   OFFS..   CODE           ?INTERRUPT?00023
000026H   000029H   000004H   BYTE   UNIT     CODE           ?PR?ADC_INT?INTERRUPT
00002AH   00002AH   000001H   BYTE   UNIT     CODE           ?PR?EX0_INT?INTERRUPT
00002BH   00002DH   000003H   BYTE   OFFS..   CODE           ?INTERRUPT?0002B
00002EH   000031H   000004H   BYTE   UNIT     CODE           ?PR?SCM_INT?INTERRUPT
000032H   000032H   000001H   ---    ---      **GAP**
000033H   000035H   000003H   BYTE   OFFS..   CODE           ?INTERRUPT?00033
000036H   000048H   000013H   BYTE   UNIT     CODE           ?PR?UARTINIT?UARTDRIVER
000049H   00004AH   000002H   ---    ---      **GAP**
00004BH   00004DH   000003H   BYTE   OFFS..   CODE           ?INTERRUPT?0004B
00004EH   000056H   000009H   BYTE   UNIT     CODE           ?PR?EX2_INT?INTERRUPT
000057H   00005AH   000004H   BYTE   UNIT     CODE           ?PR?PWM_INT?INTERRUPT
00005BH   00005DH   000003H   BYTE   OFFS..   CODE           ?INTERRUPT?0005B
00005EH   000061H   000004H   BYTE   UNIT     CODE           ?PR?ELPD_INT?INTERRUPT
000062H   000062H   000001H   ---    ---      **GAP**
000063H   000065H   000003H   BYTE   OFFS..   CODE           ?INTERRUPT?00063
000066H   00006CH   000007H   BYTE   UNIT     CODE           ?PR?UARTSENDON?UARTDRIVER
00006DH   000070H   000004H   BYTE   UNIT     CODE           ?PR?UARTINTDISABLE?UARTDRIVER
000071H   000072H   000002H   ---    ---      **GAP**
000073H   000075H   000003H   BYTE   OFFS..   CODE           ?INTERRUPT?00073
000076H   0001AEH   000139H   BYTE   UNIT     CODE           ?PR?LEDPROC?MAIN
0001AFH   0002CCH   00011EH   BYTE   UNIT     CODE           ?C?LIB_CODE
0002CDH   000389H   0000BDH   BYTE   UNIT     CODE           ?PR?EUART0_INT?INTERRUPT
00038AH   000415H   00008CH   BYTE   UNIT     CODE           ?C_C51STARTUP
000416H   000495H   000080H   BYTE   UNIT     CODE           ?PR?SCANBUTTON?MAIN
000496H   000509H   000074H   BYTE   UNIT     CODE           ?PR?USEDATAINIT?MAIN
00050AH   000572H   000069H   BYTE   UNIT     CODE           ?PR?TIMER0_INT?INTERRUPT
000573H   0005D9H   000067H   BYTE   UNIT     CODE           ?C_INITSEG
0005DAH   000628H   00004FH   BYTE   UNIT     CODE           ?PR?UART0ACK?MAIN
000629H   00066CH   000044H   BYTE   UNIT     CODE           ?C?LDIV
00066DH   0006AFH   000043H   BYTE   UNIT     CODE           ?PR?SYSINIT?MAIN
0006B0H   0006EBH   00003CH   BYTE   UNIT     CODE           ?PR?_UARTBAUDSET?UARTDRIVER
0006ECH   00071DH   000032H   BYTE   UNIT     CODE           ?PR?MAIN?MAIN
00071EH   000745H   000028H   BYTE   UNIT     CODE           ?PR?_CALCHECKSUM?MAIN
000746H   00076DH   000028H   BYTE   UNIT     CODE           ?PR?_CALCHECKSUM1?INTERRUPT
00076EH   000790H   000023H   BYTE   UNIT     CODE           ?PR?INITLEDTESTMODE?MAIN
000791H   0007B0H   000020H   BYTE   UNIT     CODE           ?PR?CLR_DATARAM?MAIN
0007B1H   0007CAH   00001AH   BYTE   UNIT     CODE           ?PR?T0INITIAL?MAIN
0007CBH   0007E4H   00001AH   BYTE   UNIT     CODE           ?PR?T1INITIAL?MAIN
0007E5H   0007FDH   000019H   BYTE   UNIT     CODE           ?PR?UART0DECODE?MAIN
0007FEH   000815H   000018H   BYTE   UNIT     CODE           ?PR?_UARTCONF?UARTDRIVER
000816H   000819H   000004H   BYTE   UNIT     CODE           ?PR?UARTINTENABLE?UARTDRIVER

* * * * * * * * * * *  X D A T A   M E M O R Y  * * * * * * * * * * * * *
000000H   000047H   000048H   BYTE   UNIT     XDATA          ?XD?INTERRUPT
000048H   000059H   000012H   BYTE   UNIT     XDATA          ?XD?MAIN
00005AH   00005BH   000002H   BYTE   UNIT     XDATA          _XDATA_GROUP_



OVERLAY MAP OF MODULE:   SH79F083_UART (?C_STARTUP)


FUNCTION/MODULE                DATA_GROUP   XDATA_GROUP
--> CALLED FUNCTION/MODULE     START  STOP  START  STOP
=======================================================
?C_C51STARTUP                  ----- -----  ----- -----
  +--> MAIN/MAIN
  +--> ?C_INITSEG

LX51 LINKER/LOCATER V4.66.93.0                                                        06/03/2025  11:44:05  PAGE 3


MAIN/MAIN                      ----- -----  ----- -----
  +--> UARTINTDISABLE/UARTDRIVER
  +--> USEDATAINIT/MAIN
  +--> SYSINIT/MAIN
  +--> UARTINIT/UARTDRIVER
  +--> _UARTBAUDSET/UARTDRIVER
  +--> _UARTCONF/UARTDRIVER
  +--> UARTINTENABLE/UARTDRIVER
  +--> INITLEDTESTMODE/MAIN
  +--> SCANBUTTON/MAIN
  +--> UART0DECODE/MAIN
  +--> UART0ACK/MAIN

UARTINTDISABLE/UARTDRIVER      ----- -----  ----- -----

USEDATAINIT/MAIN               ----- -----  ----- -----

SYSINIT/MAIN                   ----- -----  ----- -----
  +--> T0INITIAL/MAIN
  +--> T1INITIAL/MAIN
  +--> CLR_DATARAM/MAIN

T0INITIAL/MAIN                 ----- -----  ----- -----

T1INITIAL/MAIN                 ----- -----  ----- -----

CLR_DATARAM/MAIN               ----- -----  ----- -----

UARTINIT/UARTDRIVER            ----- -----  ----- -----

_UARTBAUDSET/UARTDRIVER        ----- -----  005AH 005BH

_UARTCONF/UARTDRIVER           ----- -----  ----- -----
  +--> UARTINIT/UARTDRIVER
  +--> UARTSENDON/UARTDRIVER
  +--> UARTRECEIVEON/UARTDRIVER

UARTSENDON/UARTDRIVER          ----- -----  ----- -----

UARTRECEIVEON/UARTDRIVER       ----- -----  ----- -----

UARTINTENABLE/UARTDRIVER       ----- -----  ----- -----

INITLEDTESTMODE/MAIN           ----- -----  ----- -----

SCANBUTTON/MAIN                ----- -----  005AH 005AH

UART0DECODE/MAIN               ----- -----  ----- -----

UART0ACK/MAIN                  ----- -----  ----- -----

?C_INITSEG                     ----- -----  ----- -----

*** NEW ROOT *****************

EX0_INT/INTERRUPT              ----- -----  ----- -----

*** NEW ROOT *****************

TIMER0_INT/INTERRUPT           ----- -----  ----- -----
  +--> LEDPROC/MAIN

LEDPROC/MAIN                   ----- -----  ----- -----

*** NEW ROOT *****************
LX51 LINKER/LOCATER V4.66.93.0                                                        06/03/2025  11:44:05  PAGE 4



EX1_INT/INTERRUPT              ----- -----  ----- -----

*** NEW ROOT *****************

TIMER1_INT/INTERRUPT           ----- -----  ----- -----

*** NEW ROOT *****************

EUART0_INT/INTERRUPT           0014H 0014H  ----- -----

*** NEW ROOT *****************

TIMER2_INT/INTERRUPT           ----- -----  ----- -----

*** NEW ROOT *****************

ADC_INT/INTERRUPT              ----- -----  ----- -----

*** NEW ROOT *****************

EX2_INT/INTERRUPT              ----- -----  ----- -----

*** NEW ROOT *****************

SCM_INT/INTERRUPT              ----- -----  ----- -----

*** NEW ROOT *****************

PWM_INT/INTERRUPT              ----- -----  ----- -----

*** NEW ROOT *****************

ELPD_INT/INTERRUPT             ----- -----  ----- -----



PUBLIC SYMBOLS OF MODULE:  SH79F083_UART (?C_STARTUP)


      VALUE       CLASS    TYPE      PUBLIC SYMBOL NAME
      =================================================
      0000000DH   DATA     BYTE      ?_CalCheckSum1?BYTE
      00000008H   DATA     BYTE      ?_CalCheckSum?BYTE
      010001AFH   CODE     ---       ?C?CLDOPTR
      00000000H   NUMBER   ---       ?C?CODESEG
      010001DCH   CODE     ---       ?C?CSTPTR
      01000629H   CODE     ---       ?C?SLDIV
      010001EEH   CODE     ---       ?C?ULCMP
      0100023BH   CODE     ---       ?C?ULDIV
      00000000H   NUMBER   ---       ?C?XDATASEG
      010003D1H   CODE     ---       ?C_START
      01000000H   CODE     ---       ?C_STARTUP
      0100071EH   CODE     ---       _CalCheckSum
      01000746H   CODE     ---       _CalCheckSum1
      010006B0H   CODE     ---       _UartBaudSet
      010007FEH   CODE     ---       _UartConf
*SFR* 000000D0H.6 DATA     BIT       AC
*SFR* 000000E0H   DATA     BYTE      ACC
      01000026H   CODE     ---       ADC_INT
*SFR* 00000095H   DATA     BYTE      ADCH
*SFR* 00000093H   DATA     BYTE      ADCON
*SFR* 00000097H   DATA     BYTE      ADDH
*SFR* 00000096H   DATA     BYTE      ADDL
*SFR* 00000094H   DATA     BYTE      ADT
LX51 LINKER/LOCATER V4.66.93.0                                                        06/03/2025  11:44:05  PAGE 5


*SFR* 000000F1H   DATA     BYTE      AUXC
*SFR* 000000F0H   DATA     BYTE      B
*SFR* 000000B0H.2 DATA     BIT       BUTTON
      02000054H   XDATA    BYTE      BUTTON_Buff
      02000052H   XDATA    BYTE      BUTTON_Index
      02000057H   XDATA    BYTE      BUTTON_New
      02000055H   XDATA    BYTE      BUTTON_Old
      02000053H   XDATA    BYTE      BUTTON_Value
      02000058H   XDATA    BYTE      BUTTON_Value1
      0200004DH   XDATA    ---       BUTTON_Value_Buff
      0200004AH   XDATA    BYTE      ButtonID
*SFR* 000000C8H.1 DATA     BIT       C_T2
      02000056H   XDATA    BYTE      Check
*SFR* 000000B2H   DATA     BYTE      CLKCON
      01000791H   CODE     ---       CLR_DataRAM
*SFR* 000000C8H.0 DATA     BIT       CP_RL2
*SFR* 000000D0H.7 DATA     BIT       CY
*SFR* 00000083H   DATA     BYTE      DPH
*SFR* 00000085H   DATA     BYTE      DPH1
*SFR* 00000082H   DATA     BYTE      DPL
*SFR* 00000084H   DATA     BYTE      DPL1
*SFR* 000000A8H.7 DATA     BIT       EA
*SFR* 000000A8H.6 DATA     BIT       EADC
      0100005EH   CODE     ---       ELPD_INT
*SFR* 000000A8H.4 DATA     BIT       ES0
*SFR* 000000A8H.1 DATA     BIT       ET0
*SFR* 000000A8H.3 DATA     BIT       ET1
*SFR* 000000A8H.5 DATA     BIT       ET2
      010002CDH   CODE     ---       EUART0_INT
*SFR* 000000A8H.0 DATA     BIT       EX0
      0100002AH   CODE     ---       EX0_INT
*SFR* 000000A8H.2 DATA     BIT       EX1
      01000006H   CODE     ---       EX1_INT
      0100004EH   CODE     ---       EX2_INT
*SFR* 000000C8H.3 DATA     BIT       EXEN2
*SFR* 000000E8H   DATA     BYTE      EXF0
*SFR* 000000C8H.6 DATA     BIT       EXF2
*SFR* 000000D0H.5 DATA     BIT       F0
*SFR* 000000D0H.1 DATA     BIT       F1
*SFR* 000000A7H   DATA     BYTE      FLASHCON
      02000001H   XDATA    BYTE      gb500msCnt485
      00000021H.2 BIT      BIT       gb_TestMode
      02000048H   XDATA    WORD      gb_TestModeCount3s
      00000032H   DATA     BYTE      gB_Uart0_1ms
      00000020H.5 BIT      BIT       gbFg10msCH1
      00000020H.3 BIT      BIT       gbFg5msCH1
      02000051H   XDATA    BYTE      gBFg_100ms
      00000021H.1 BIT      BIT       gBFg_1ms
      00000020H.7 BIT      BIT       gbFg_BUTTON_Flag
      00000021H.0 BIT      BIT       gBFg_ScanButton_1ms
      00000020H.6 BIT      BIT       gBFg_Uart0_1ms_ack
      00000020H.2 BIT      BIT       gbFg_Uart0Ack
      00000020H.0 BIT      BIT       gbFg_Uart0RxEnd
      00000020H.4 BIT      BIT       gbFg_Uart0SendRec
      00000020H.1 BIT      BIT       gbFg_Uart1ReOk
      02000004H   XDATA    ---       gbUart0Buff
      02000026H   XDATA    ---       gbUart0Buff1
      02000003H   XDATA    BYTE      gbUart0Count500ms
      02000002H   XDATA    BYTE      gbUart0Head
      02000025H   XDATA    BYTE      gbUart0Len
      02000000H   XDATA    BYTE      gbUart0RxLen
      02000047H   XDATA    BYTE      gbUart0TxdLen
*SFR* 000000F2H   DATA     BYTE      IB_CON1
*SFR* 000000F3H   DATA     BYTE      IB_CON2
*SFR* 000000F4H   DATA     BYTE      IB_CON3
LX51 LINKER/LOCATER V4.66.93.0                                                        06/03/2025  11:44:05  PAGE 6


*SFR* 000000F5H   DATA     BYTE      IB_CON4
*SFR* 000000F6H   DATA     BYTE      IB_CON5
*SFR* 000000FCH   DATA     BYTE      IB_DATA
*SFR* 000000FBH   DATA     BYTE      IB_OFFSET
*SFR* 00000088H.1 DATA     BIT       IE0
*SFR* 00000088H.3 DATA     BIT       IE1
*SFR* 000000E8H.0 DATA     BIT       IE2
*SFR* 000000A8H   DATA     BYTE      IEN0
*SFR* 000000A9H   DATA     BYTE      IEN1
      0100076EH   CODE     ---       InitLedTestMode
*SFR* 00000086H   DATA     BYTE      INSCON
*SFR* 000000B4H   DATA     BYTE      IPH0
*SFR* 000000B5H   DATA     BYTE      IPH1
*SFR* 000000B8H   DATA     BYTE      IPL0
*SFR* 000000B9H   DATA     BYTE      IPL1
*SFR* 00000088H.0 DATA     BIT       IT0
*SFR* 00000088H.2 DATA     BIT       IT1
*SFR* 000000E8H.2 DATA     BIT       IT20
*SFR* 000000E8H.3 DATA     BIT       IT21
*SFR* 00000090H.7 DATA     BIT       LED1
      0000003BH   DATA     DWORD     LED1_Count
      00000022H   DATA     WORD      LED1_Off
      00000033H   DATA     DWORD     LED1_Off_Count
      0000002EH   DATA     WORD      LED1_On
      00000026H   DATA     DWORD     LED1_On_Count
*SFR* 00000090H.6 DATA     BIT       LED2
      0000003FH   DATA     DWORD     LED2_Count
      00000024H   DATA     WORD      LED2_Off
      00000037H   DATA     DWORD     LED2_Off_Count
      00000030H   DATA     WORD      LED2_On
      0000002AH   DATA     DWORD     LED2_On_Count
      01000076H   CODE     ---       LEDProc
      02000059H   XDATA    BYTE      Lenth
      00000013H   DATA     BYTE      loop_20ms_exit
*SFR* 000000B3H   DATA     BYTE      LPDCON
      010006ECH   CODE     ---       main
      0200004BH   XDATA    BYTE      OBJ_CMD
*SFR* 000000D0H.2 DATA     BIT       OV
*SFR* 000000D0H.0 DATA     BIT       P
*SFR* 00000090H   DATA     BYTE      P1
*SFR* 00000090H.2 DATA     BIT       P1_2
*SFR* 00000090H.3 DATA     BIT       P1_3
*SFR* 00000090H.4 DATA     BIT       P1_4
*SFR* 00000090H.5 DATA     BIT       P1_5
*SFR* 00000090H.6 DATA     BIT       P1_6
*SFR* 00000090H.7 DATA     BIT       P1_7
*SFR* 000000EAH   DATA     BYTE      P1M0
*SFR* 000000E2H   DATA     BYTE      P1M1
*SFR* 000000B0H   DATA     BYTE      P3
*SFR* 000000B0H.0 DATA     BIT       P3_0
*SFR* 000000B0H.1 DATA     BIT       P3_1
*SFR* 000000B0H.2 DATA     BIT       P3_2
*SFR* 000000B0H.3 DATA     BIT       P3_3
*SFR* 000000B0H.7 DATA     BIT       P3_7
*SFR* 000000ECH   DATA     BYTE      P3M0
*SFR* 000000E4H   DATA     BYTE      P3M1
*SFR* 000000C0H   DATA     BYTE      P4
*SFR* 000000C0H.0 DATA     BIT       P4_0
*SFR* 000000C0H.1 DATA     BIT       P4_1
*SFR* 000000C0H.2 DATA     BIT       P4_2
*SFR* 000000EDH   DATA     BYTE      P4M0
*SFR* 000000E5H   DATA     BYTE      P4M1
*SFR* 000000B8H.6 DATA     BIT       PADCL
*SFR* 00000087H   DATA     BYTE      PCON
*SFR* 000000B8H.4 DATA     BIT       PSL
LX51 LINKER/LOCATER V4.66.93.0                                                        06/03/2025  11:44:05  PAGE 7


*SFR* 000000D0H   DATA     BYTE      PSW
*SFR* 000000B8H.1 DATA     BIT       PT0L
*SFR* 000000B8H.3 DATA     BIT       PT1L
*SFR* 000000B8H.5 DATA     BIT       PT2L
      01000057H   CODE     ---       PWM_INT
*SFR* 000000D1H   DATA     BYTE      PWMCON
*SFR* 000000D3H   DATA     BYTE      PWMD
*SFR* 000000D2H   DATA     BYTE      PWMP
*SFR* 000000B8H.0 DATA     BIT       PX0L
*SFR* 000000B8H.2 DATA     BIT       PX1L
*SFR* 00000098H.2 DATA     BIT       RB8
*SFR* 000000CBH   DATA     BYTE      RCAP2H
*SFR* 000000CAH   DATA     BYTE      RCAP2L
*SFR* 000000C8H.5 DATA     BIT       RCLK
*SFR* 00000098H.4 DATA     BIT       REN
*SFR* 00000098H.0 DATA     BIT       RI
*SFR* 000000D0H.3 DATA     BIT       RS0
*SFR* 000000D0H.4 DATA     BIT       RS1
*SFR* 000000B1H   DATA     BYTE      RSTSTAT
*SFR* 0000009AH   DATA     BYTE      SADDR
*SFR* 0000009BH   DATA     BYTE      SADEN
*SFR* 00000099H   DATA     BYTE      SBUF
      00000012H   DATA     BYTE      SBUFTemp
      01000416H   CODE     ---       ScanButton
      0100002EH   CODE     ---       SCM_INT
*SFR* 00000098H   DATA     BYTE      SCON
*SFR* 00000098H.7 DATA     BIT       SM0_FE
*SFR* 00000098H.6 DATA     BIT       SM1_RXOV
*SFR* 00000098H.5 DATA     BIT       SM2_TXCOL
*SFR* 00000081H   DATA     BYTE      SP
*SFR* 0000008EH   DATA     BYTE      SUSLO
      0100066DH   CODE     ---       SysInit
      010007B1H   CODE     ---       T0Initial
      010007CBH   CODE     ---       T1Initial
*SFR* 000000C8H   DATA     BYTE      T2CON
*SFR* 000000C9H   DATA     BYTE      T2MOD
*SFR* 00000098H.3 DATA     BIT       TB8
*SFR* 000000C8H.4 DATA     BIT       TCLK
*SFR* 00000088H   DATA     BYTE      TCON
*SFR* 000000CEH   DATA     BYTE      TCON1
*SFR* 00000088H.5 DATA     BIT       TF0
*SFR* 00000088H.7 DATA     BIT       TF1
*SFR* 000000C8H.7 DATA     BIT       TF2
*SFR* 0000008CH   DATA     BYTE      TH0
*SFR* 0000008DH   DATA     BYTE      TH1
*SFR* 000000CDH   DATA     BYTE      TH2
*SFR* 00000098H.1 DATA     BIT       TI
      0100050AH   CODE     ---       Timer0_INT
      0100000EH   CODE     ---       Timer1_INT
      01000016H   CODE     ---       Timer2_INT
*SFR* 0000008AH   DATA     BYTE      TL0
*SFR* 0000008BH   DATA     BYTE      TL1
*SFR* 000000CCH   DATA     BYTE      TL2
*SFR* 00000089H   DATA     BYTE      TMOD
*SFR* 00000088H.4 DATA     BIT       TR0
*SFR* 00000088H.6 DATA     BIT       TR1
*SFR* 000000C8H.2 DATA     BIT       TR2
      010005DAH   CODE     ---       Uart0Ack
      010007E5H   CODE     ---       Uart0Decode
      01000036H   CODE     ---       UartInit
      0100006DH   CODE     ---       UartIntDisable
      01000816H   CODE     ---       UartIntEnable
      0100001EH   CODE     ---       UARTReceiveOn
      01000066H   CODE     ---       UARTSendOn
      0200004CH   XDATA    BYTE      UpOrDown
LX51 LINKER/LOCATER V4.66.93.0                                                        06/03/2025  11:44:05  PAGE 8


      01000496H   CODE     ---       UseDataInit
*SFR* 000000F7H   DATA     BYTE      XPAGE



SYMBOL TABLE OF MODULE:  SH79F083_UART (?C_STARTUP)

      VALUE       REP       CLASS    TYPE      SYMBOL NAME
      ====================================================
      ---         MODULE    ---      ---       ?C_STARTUP
      01000000H   PUBLIC    CODE     ---       ?C_STARTUP
      000000E0H   SYMBOL    DATA     ---       ACC
      000000F0H   SYMBOL    DATA     ---       B
      00000083H   SYMBOL    DATA     ---       DPH
      00000082H   SYMBOL    DATA     ---       DPL
      00000000H   SYMBOL    NUMBER   ---       IBPSTACK
      00000100H   SYMBOL    NUMBER   ---       IBPSTACKTOP
      00000080H   SYMBOL    NUMBER   ---       IDATALEN
      0100038DH   SYMBOL    CODE     ---       IDATALOOP
      00000000H   SYMBOL    NUMBER   ---       PBPSTACK
      00000100H   SYMBOL    NUMBER   ---       PBPSTACKTOP
      00000000H   SYMBOL    NUMBER   ---       PDATALEN
      00000000H   SYMBOL    NUMBER   ---       PDATASTART
      00000000H   SYMBOL    NUMBER   ---       PPAGE
      00000000H   SYMBOL    NUMBER   ---       PPAGEENABLE
      000000A0H   SYMBOL    DATA     ---       PPAGE_SFR
      00000081H   SYMBOL    DATA     ---       SP
      0100038AH   SYMBOL    CODE     ---       STARTUP1
      00000000H   SYMBOL    NUMBER   ---       XBPSTACK
      00000000H   SYMBOL    NUMBER   ---       XBPSTACKTOP
      00000000H   SYMBOL    NUMBER   ---       XDATALEN
      00000000H   SYMBOL    NUMBER   ---       XDATASTART
      01000000H   LINE      CODE     ---       #126
      0100038AH   LINE      CODE     ---       #133
      0100038CH   LINE      CODE     ---       #134
      0100038DH   LINE      CODE     ---       #135
      0100038EH   LINE      CODE     ---       #136
      01000390H   LINE      CODE     ---       #185
      01000393H   LINE      CODE     ---       #196

      ---         MODULE    ---      ---       MAIN
      02000059H   PUBLIC    XDATA    BYTE      Lenth
      0000003FH   PUBLIC    DATA     DWORD     LED2_Count
      0000003BH   PUBLIC    DATA     DWORD     LED1_Count
      00000037H   PUBLIC    DATA     DWORD     LED2_Off_Count
      00000033H   PUBLIC    DATA     DWORD     LED1_Off_Count
      02000058H   PUBLIC    XDATA    BYTE      BUTTON_Value1
      02000057H   PUBLIC    XDATA    BYTE      BUTTON_New
      02000056H   PUBLIC    XDATA    BYTE      Check
      02000055H   PUBLIC    XDATA    BYTE      BUTTON_Old
      00000021H.2 PUBLIC    BIT      BIT       gb_TestMode
      00000032H   PUBLIC    DATA     BYTE      gB_Uart0_1ms
      00000021H.1 PUBLIC    BIT      BIT       gBFg_1ms
      02000054H   PUBLIC    XDATA    BYTE      BUTTON_Buff
      02000053H   PUBLIC    XDATA    BYTE      BUTTON_Value
      02000052H   PUBLIC    XDATA    BYTE      BUTTON_Index
      00000021H.0 PUBLIC    BIT      BIT       gBFg_ScanButton_1ms
      02000051H   PUBLIC    XDATA    BYTE      gBFg_100ms
      00000030H   PUBLIC    DATA     WORD      LED2_On
      0000002EH   PUBLIC    DATA     WORD      LED1_On
      0200004DH   PUBLIC    XDATA    ---       BUTTON_Value_Buff
      0200004CH   PUBLIC    XDATA    BYTE      UpOrDown
      00000020H.7 PUBLIC    BIT      BIT       gbFg_BUTTON_Flag
      0000002AH   PUBLIC    DATA     DWORD     LED2_On_Count
      00000026H   PUBLIC    DATA     DWORD     LED1_On_Count
LX51 LINKER/LOCATER V4.66.93.0                                                        06/03/2025  11:44:05  PAGE 9


      0200004BH   PUBLIC    XDATA    BYTE      OBJ_CMD
      0200004AH   PUBLIC    XDATA    BYTE      ButtonID
      00000020H.6 PUBLIC    BIT      BIT       gBFg_Uart0_1ms_ack
      02000048H   PUBLIC    XDATA    WORD      gb_TestModeCount3s
      00000024H   PUBLIC    DATA     WORD      LED2_Off
      00000022H   PUBLIC    DATA     WORD      LED1_Off
      010006ECH   PUBLIC    CODE     ---       main
      010005DAH   PUBLIC    CODE     ---       Uart0Ack
      010007E5H   PUBLIC    CODE     ---       Uart0Decode
      00000008H   PUBLIC    DATA     BYTE      ?_CalCheckSum?BYTE
      0100071EH   PUBLIC    CODE     ---       _CalCheckSum
      01000416H   PUBLIC    CODE     ---       ScanButton
      01000076H   PUBLIC    CODE     ---       LEDProc
      0100076EH   PUBLIC    CODE     ---       InitLedTestMode
      010007CBH   PUBLIC    CODE     ---       T1Initial
      010007B1H   PUBLIC    CODE     ---       T0Initial
      01000496H   PUBLIC    CODE     ---       UseDataInit
      0100066DH   PUBLIC    CODE     ---       SysInit
      01000791H   PUBLIC    CODE     ---       CLR_DataRAM
      000000EAH   SFRSYM    DATA     BYTE      P1M0
      000000F3H   SFRSYM    DATA     BYTE      IB_CON2
      000000E8H.2 SFRSYM    DATA     BIT       IT20
      000000E2H   SFRSYM    DATA     BYTE      P1M1
      000000F4H   SFRSYM    DATA     BYTE      IB_CON3
      000000E8H.3 SFRSYM    DATA     BIT       IT21
      000000ECH   SFRSYM    DATA     BYTE      P3M0
      00000090H   SFRSYM    DATA     BYTE      P1
      000000F5H   SFRSYM    DATA     BYTE      IB_CON4
      000000EDH   SFRSYM    DATA     BYTE      P4M0
      000000E4H   SFRSYM    DATA     BYTE      P3M1
      000000F6H   SFRSYM    DATA     BYTE      IB_CON5
      00000098H.6 SFRSYM    DATA     BIT       SM1_RXOV
      000000E5H   SFRSYM    DATA     BYTE      P4M1
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000A8H.7 SFRSYM    DATA     BIT       EA
      00000090H.7 SFRSYM    DATA     BIT       LED1
      000000FCH   SFRSYM    DATA     BYTE      IB_DATA
      00000090H.6 SFRSYM    DATA     BIT       LED2
      000000A8H   SFRSYM    DATA     BYTE      IEN0
      000000A8H.6 SFRSYM    DATA     BIT       EADC
      000000A9H   SFRSYM    DATA     BYTE      IEN1
      00000085H   SFRSYM    DATA     BYTE      DPH1
      00000095H   SFRSYM    DATA     BYTE      ADCH
      00000097H   SFRSYM    DATA     BYTE      ADDH
      000000B4H   SFRSYM    DATA     BYTE      IPH0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000B0H.0 SFRSYM    DATA     BIT       P3_0
      00000090H.2 SFRSYM    DATA     BIT       P1_2
      000000B5H   SFRSYM    DATA     BYTE      IPH1
      000000C0H.0 SFRSYM    DATA     BIT       P4_0
      000000B0H.1 SFRSYM    DATA     BIT       P3_1
      00000090H.3 SFRSYM    DATA     BIT       P1_3
      000000E8H   SFRSYM    DATA     BYTE      EXF0
      000000C0H.1 SFRSYM    DATA     BIT       P4_1
      000000B0H.2 SFRSYM    DATA     BIT       P3_2
      00000090H.4 SFRSYM    DATA     BIT       P1_4
      000000C0H.2 SFRSYM    DATA     BIT       P4_2
      000000B0H.3 SFRSYM    DATA     BIT       P3_3
      00000090H.5 SFRSYM    DATA     BIT       P1_5
      000000C8H.6 SFRSYM    DATA     BIT       EXF2
      00000096H   SFRSYM    DATA     BYTE      ADDL
      000000B8H   SFRSYM    DATA     BYTE      IPL0
      00000090H.6 SFRSYM    DATA     BIT       P1_6
LX51 LINKER/LOCATER V4.66.93.0                                                        06/03/2025  11:44:05  PAGE 10


      000000B9H   SFRSYM    DATA     BYTE      IPL1
      00000090H.7 SFRSYM    DATA     BIT       P1_7
      0000008EH   SFRSYM    DATA     BYTE      SUSLO
      000000B0H.7 SFRSYM    DATA     BIT       P3_7
      00000098H.0 SFRSYM    DATA     BIT       RI
      000000D0H.7 SFRSYM    DATA     BIT       CY
      00000098H.1 SFRSYM    DATA     BIT       TI
      000000B8H.1 SFRSYM    DATA     BIT       PT0L
      000000B8H.3 SFRSYM    DATA     BIT       PT1L
      000000CBH   SFRSYM    DATA     BYTE      RCAP2H
      000000B8H.5 SFRSYM    DATA     BIT       PT2L
      00000081H   SFRSYM    DATA     BYTE      SP
      000000B8H.0 SFRSYM    DATA     BIT       PX0L
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000B8H.2 SFRSYM    DATA     BIT       PX1L
      000000CAH   SFRSYM    DATA     BYTE      RCAP2L
      000000C8H.1 SFRSYM    DATA     BIT       C_T2
      000000C8H.5 SFRSYM    DATA     BIT       RCLK
      000000C8H.4 SFRSYM    DATA     BIT       TCLK
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000F1H   SFRSYM    DATA     BYTE      AUXC
      00000098H   SFRSYM    DATA     BYTE      SCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000FBH   SFRSYM    DATA     BYTE      IB_OFFSET
      000000B1H   SFRSYM    DATA     BYTE      RSTSTAT
      000000D3H   SFRSYM    DATA     BYTE      PWMD
      00000098H.7 SFRSYM    DATA     BIT       SM0_FE
      000000B2H   SFRSYM    DATA     BYTE      CLKCON
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      00000098H.5 SFRSYM    DATA     BIT       SM2_TXCOL
      000000E8H.0 SFRSYM    DATA     BIT       IE2
      000000F0H   SFRSYM    DATA     BYTE      B
      000000B3H   SFRSYM    DATA     BYTE      LPDCON
      000000C8H.0 SFRSYM    DATA     BIT       CP_RL2
      000000D2H   SFRSYM    DATA     BYTE      PWMP
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.7 SFRSYM    DATA     BIT       TF1
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.2 SFRSYM    DATA     BIT       RB8
      000000C8H.7 SFRSYM    DATA     BIT       TF2
      0000008CH   SFRSYM    DATA     BYTE      TH0
      00000086H   SFRSYM    DATA     BYTE      INSCON
      00000088H.0 SFRSYM    DATA     BIT       IT0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      00000098H.3 SFRSYM    DATA     BIT       TB8
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      000000A7H   SFRSYM    DATA     BYTE      FLASHCON
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000D1H   SFRSYM    DATA     BYTE      PWMCON
LX51 LINKER/LOCATER V4.66.93.0                                                        06/03/2025  11:44:05  PAGE 11


      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000C8H.2 SFRSYM    DATA     BIT       TR2
      00000094H   SFRSYM    DATA     BYTE      ADT
      00000083H   SFRSYM    DATA     BYTE      DPH
      000000B0H.2 SFRSYM    DATA     BIT       BUTTON
      00000082H   SFRSYM    DATA     BYTE      DPL
      000000C8H.3 SFRSYM    DATA     BIT       EXEN2
      00000098H.4 SFRSYM    DATA     BIT       REN
      000000B8H.6 SFRSYM    DATA     BIT       PADCL
      00000093H   SFRSYM    DATA     BYTE      ADCON
      000000CEH   SFRSYM    DATA     BYTE      TCON1
      000000C9H   SFRSYM    DATA     BYTE      T2MOD
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      0000009BH   SFRSYM    DATA     BYTE      SADEN
      000000B8H.4 SFRSYM    DATA     BIT       PSL
      0000009AH   SFRSYM    DATA     BYTE      SADDR
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000D0H.1 SFRSYM    DATA     BIT       F1
      000000F7H   SFRSYM    DATA     BYTE      XPAGE
      000000D0H   SFRSYM    DATA     BYTE      PSW
      000000F2H   SFRSYM    DATA     BYTE      IB_CON1

      01000791H   BLOCK     CODE     ---       LVL=0
      01000791H   BLOCK     CODE     NEAR LAB  LVL=1
      00000001H   SYMBOL    DATA     ---       p
      00000007H   SYMBOL    DATA     BYTE      i
      ---         BLOCKEND  ---      ---       LVL=1
      01000791H   LINE      CODE     ---       #140
      01000791H   LINE      CODE     ---       #141
      01000791H   LINE      CODE     ---       #145
      01000797H   LINE      CODE     ---       #146
      010007B0H   LINE      CODE     ---       #147
      ---         BLOCKEND  ---      ---       LVL=0

      0100066DH   BLOCK     CODE     ---       LVL=0
      0100066DH   LINE      CODE     ---       #162
      0100066DH   LINE      CODE     ---       #163
      0100066DH   LINE      CODE     ---       #164
      0100066FH   LINE      CODE     ---       #166
      01000672H   LINE      CODE     ---       #167
      01000675H   LINE      CODE     ---       #168
      01000677H   LINE      CODE     ---       #170
      0100067AH   LINE      CODE     ---       #171
      0100067DH   LINE      CODE     ---       #173
      0100067FH   LINE      CODE     ---       #174
      01000682H   LINE      CODE     ---       #176
      01000684H   LINE      CODE     ---       #177
      01000687H   LINE      CODE     ---       #180
      01000689H   LINE      CODE     ---       #187
      0100068CH   LINE      CODE     ---       #188
      0100068FH   LINE      CODE     ---       #189
      01000691H   LINE      CODE     ---       #190
      01000693H   LINE      CODE     ---       #194
      01000696H   LINE      CODE     ---       #197
      01000699H   LINE      CODE     ---       #204
      0100069CH   LINE      CODE     ---       #208
      0100069FH   LINE      CODE     ---       #211
      010006A1H   LINE      CODE     ---       #213
      010006A4H   LINE      CODE     ---       #216
      010006A7H   LINE      CODE     ---       #217
      010006AAH   LINE      CODE     ---       #219
      010006ADH   LINE      CODE     ---       #221
      ---         BLOCKEND  ---      ---       LVL=0

      01000496H   BLOCK     CODE     ---       LVL=0
LX51 LINKER/LOCATER V4.66.93.0                                                        06/03/2025  11:44:05  PAGE 12


      01000496H   BLOCK     CODE     NEAR LAB  LVL=1
      00000007H   SYMBOL    DATA     BYTE      i
      ---         BLOCKEND  ---      ---       LVL=1
      01000496H   LINE      CODE     ---       #251
      01000496H   LINE      CODE     ---       #252
      01000496H   LINE      CODE     ---       #255
      0100049BH   LINE      CODE     ---       #256
      0100049FH   LINE      CODE     ---       #257
      010004A1H   LINE      CODE     ---       #258
      010004A3H   LINE      CODE     ---       #259
      010004AEH   LINE      CODE     ---       #260
      010004AEH   LINE      CODE     ---       #261
      010004BAH   LINE      CODE     ---       #262
      010004BDH   LINE      CODE     ---       #264
      010004C2H   LINE      CODE     ---       #265
      010004C6H   LINE      CODE     ---       #266
      010004CAH   LINE      CODE     ---       #267
      010004CEH   LINE      CODE     ---       #269
      010004D0H   LINE      CODE     ---       #270
      010004D4H   LINE      CODE     ---       #272
      010004D8H   LINE      CODE     ---       #273
      010004DCH   LINE      CODE     ---       #274
      010004E0H   LINE      CODE     ---       #275
      010004E4H   LINE      CODE     ---       #276
      010004E8H   LINE      CODE     ---       #277
      010004ECH   LINE      CODE     ---       #278
      010004EEH   LINE      CODE     ---       #279
      010004F0H   LINE      CODE     ---       #280
      010004F2H   LINE      CODE     ---       #282
      010004F8H   LINE      CODE     ---       #283
      010004F8H   LINE      CODE     ---       #284
      010004F8H   LINE      CODE     ---       #285
      010004F8H   LINE      CODE     ---       #286
      010004F8H   LINE      CODE     ---       #287
      010004F8H   LINE      CODE     ---       #288
      010004FAH   LINE      CODE     ---       #290
      010004FAH   LINE      CODE     ---       #291
      010004FDH   LINE      CODE     ---       #292
      01000501H   LINE      CODE     ---       #293
      01000505H   LINE      CODE     ---       #294
      01000509H   LINE      CODE     ---       #295
      01000509H   LINE      CODE     ---       #296
      ---         BLOCKEND  ---      ---       LVL=0

      010007B1H   BLOCK     CODE     ---       LVL=0
      010007B1H   LINE      CODE     ---       #301
      010007B1H   LINE      CODE     ---       #302
      010007B1H   LINE      CODE     ---       #303
      010007B4H   LINE      CODE     ---       #305
      010007B7H   LINE      CODE     ---       #306
      010007BAH   LINE      CODE     ---       #309
      010007BDH   LINE      CODE     ---       #310
      010007C0H   LINE      CODE     ---       #313
      010007C3H   LINE      CODE     ---       #314
      010007C6H   LINE      CODE     ---       #315
      010007C8H   LINE      CODE     ---       #316
      010007CAH   LINE      CODE     ---       #317
      ---         BLOCKEND  ---      ---       LVL=0

      010007CBH   BLOCK     CODE     ---       LVL=0
      010007CBH   LINE      CODE     ---       #321
      010007CBH   LINE      CODE     ---       #322
      010007CBH   LINE      CODE     ---       #323
      010007CEH   LINE      CODE     ---       #325
      010007D1H   LINE      CODE     ---       #326
LX51 LINKER/LOCATER V4.66.93.0                                                        06/03/2025  11:44:05  PAGE 13


      010007D4H   LINE      CODE     ---       #329
      010007D7H   LINE      CODE     ---       #330
      010007DAH   LINE      CODE     ---       #333
      010007DDH   LINE      CODE     ---       #334
      010007E0H   LINE      CODE     ---       #335
      010007E2H   LINE      CODE     ---       #336
      010007E4H   LINE      CODE     ---       #337
      ---         BLOCKEND  ---      ---       LVL=0

      0100076EH   BLOCK     CODE     ---       LVL=0
      0100076EH   LINE      CODE     ---       #341
      0100076EH   LINE      CODE     ---       #342
      0100076EH   LINE      CODE     ---       #343
      01000771H   LINE      CODE     ---       #344
      01000771H   LINE      CODE     ---       #345
      01000773H   LINE      CODE     ---       #346
      0100077AH   LINE      CODE     ---       #347
      0100077DH   LINE      CODE     ---       #348
      01000785H   LINE      CODE     ---       #349
      01000788H   LINE      CODE     ---       #350
      01000790H   LINE      CODE     ---       #351
      01000790H   LINE      CODE     ---       #352
      ---         BLOCKEND  ---      ---       LVL=0

      01000076H   BLOCK     CODE     ---       LVL=0
      01000076H   LINE      CODE     ---       #356
      01000076H   LINE      CODE     ---       #357
      01000076H   LINE      CODE     ---       #358
      0100007CH   LINE      CODE     ---       #359
      0100007CH   LINE      CODE     ---       #360
      0100007EH   LINE      CODE     ---       #362
      01000084H   LINE      CODE     ---       #363
      01000091H   LINE      CODE     ---       #364
      01000091H   LINE      CODE     ---       #365
      01000093H   LINE      CODE     ---       #373
      01000099H   LINE      CODE     ---       #374
      01000099H   LINE      CODE     ---       #375
      010000A7H   LINE      CODE     ---       #376
      010000B6H   LINE      CODE     ---       #377
      010000B6H   LINE      CODE     ---       #378
      010000BAH   LINE      CODE     ---       #379
      010000BCH   LINE      CODE     ---       #380
      010000BDH   LINE      CODE     ---       #382
      010000BDH   LINE      CODE     ---       #383
      010000D2H   LINE      CODE     ---       #384
      010000E6H   LINE      CODE     ---       #385
      010000E6H   LINE      CODE     ---       #386
      010000E6H   LINE      CODE     ---       #387
      010000E9H   LINE      CODE     ---       #388
      010000FDH   LINE      CODE     ---       #389
      010000FDH   LINE      CODE     ---       #390
      010000FDH   LINE      CODE     ---       #391
      01000100H   LINE      CODE     ---       #392
      01000114H   LINE      CODE     ---       #393
      01000114H   LINE      CODE     ---       #394
      01000117H   LINE      CODE     ---       #395
      01000117H   LINE      CODE     ---       #396
      0100011AH   LINE      CODE     ---       #398
      0100011AH   LINE      CODE     ---       #399
      0100011BH   LINE      CODE     ---       #400
      0100011BH   LINE      CODE     ---       #401
      0100011BH   LINE      CODE     ---       #402
      0100011EH   LINE      CODE     ---       #405
      0100011EH   LINE      CODE     ---       #406
      01000124H   LINE      CODE     ---       #407
LX51 LINKER/LOCATER V4.66.93.0                                                        06/03/2025  11:44:05  PAGE 14


      01000124H   LINE      CODE     ---       #408
      01000124H   LINE      CODE     ---       #409
      01000124H   LINE      CODE     ---       #410
      01000126H   LINE      CODE     ---       #411
      01000132H   LINE      CODE     ---       #412
      01000132H   LINE      CODE     ---       #413
      01000135H   LINE      CODE     ---       #414
      01000135H   LINE      CODE     ---       #415
      01000137H   LINE      CODE     ---       #417
      01000137H   LINE      CODE     ---       #418
      0100013DH   LINE      CODE     ---       #419
      0100013DH   LINE      CODE     ---       #420
      01000140H   LINE      CODE     ---       #421
      01000140H   LINE      CODE     ---       #422
      01000142H   LINE      CODE     ---       #424
      01000142H   LINE      CODE     ---       #425
      01000157H   LINE      CODE     ---       #426
      0100016CH   LINE      CODE     ---       #427
      0100016CH   LINE      CODE     ---       #428
      0100016FH   LINE      CODE     ---       #429
      01000170H   LINE      CODE     ---       #430
      01000186H   LINE      CODE     ---       #431
      01000186H   LINE      CODE     ---       #432
      01000189H   LINE      CODE     ---       #433
      0100018AH   LINE      CODE     ---       #434
      010001A0H   LINE      CODE     ---       #435
      010001A0H   LINE      CODE     ---       #436
      010001A3H   LINE      CODE     ---       #437
      010001A3H   LINE      CODE     ---       #438
      010001A5H   LINE      CODE     ---       #440
      010001A5H   LINE      CODE     ---       #441
      010001AEH   LINE      CODE     ---       #442
      010001AEH   LINE      CODE     ---       #443
      010001AEH   LINE      CODE     ---       #444
      010001AEH   LINE      CODE     ---       #445
      010001AEH   LINE      CODE     ---       #446
      010001AEH   LINE      CODE     ---       #447
      010001AEH   LINE      CODE     ---       #448
      ---         BLOCKEND  ---      ---       LVL=0

      01000416H   BLOCK     CODE     ---       LVL=0
      01000416H   BLOCK     CODE     NEAR LAB  LVL=1
      0200005AH   SYMBOL    XDATA    BYTE      temp
      ---         BLOCKEND  ---      ---       LVL=1
      01000416H   LINE      CODE     ---       #544
      01000416H   LINE      CODE     ---       #545
      01000416H   LINE      CODE     ---       #546
      0100041BH   LINE      CODE     ---       #548
      0100041EH   LINE      CODE     ---       #549
      0100041EH   LINE      CODE     ---       #550
      01000420H   LINE      CODE     ---       #552
      01000423H   LINE      CODE     ---       #553
      01000426H   LINE      CODE     ---       #554
      01000429H   LINE      CODE     ---       #555
      0100042AH   LINE      CODE     ---       #556
      0100042BH   LINE      CODE     ---       #557
      0100042CH   LINE      CODE     ---       #560
      01000432H   LINE      CODE     ---       #561
      01000432H   LINE      CODE     ---       #562
      01000433H   LINE      CODE     ---       #563
      01000435H   LINE      CODE     ---       #565
      01000435H   LINE      CODE     ---       #566
      01000438H   LINE      CODE     ---       #567
      01000438H   LINE      CODE     ---       #569
      01000445H   LINE      CODE     ---       #570
LX51 LINKER/LOCATER V4.66.93.0                                                        06/03/2025  11:44:05  PAGE 15


      01000445H   LINE      CODE     ---       #571
      01000447H   LINE      CODE     ---       #572
      01000448H   LINE      CODE     ---       #574
      01000448H   LINE      CODE     ---       #575
      0100044FH   LINE      CODE     ---       #576
      0100044FH   LINE      CODE     ---       #577
      01000455H   LINE      CODE     ---       #578
      01000455H   LINE      CODE     ---       #579
      0100045AH   LINE      CODE     ---       #580
      0100045AH   LINE      CODE     ---       #581
      0100045CH   LINE      CODE     ---       #583
      0100045CH   LINE      CODE     ---       #584
      01000462H   LINE      CODE     ---       #585
      01000466H   LINE      CODE     ---       #586
      01000466H   LINE      CODE     ---       #587
      01000467H   LINE      CODE     ---       #589
      01000467H   LINE      CODE     ---       #590
      0100046EH   LINE      CODE     ---       #591
      0100046EH   LINE      CODE     ---       #592
      01000473H   LINE      CODE     ---       #593
      01000477H   LINE      CODE     ---       #594
      01000479H   LINE      CODE     ---       #596
      01000479H   LINE      CODE     ---       #597
      0100047FH   LINE      CODE     ---       #598
      01000483H   LINE      CODE     ---       #600
      01000489H   LINE      CODE     ---       #601
      01000489H   LINE      CODE     ---       #602
      0100048BH   LINE      CODE     ---       #603
      0100048CH   LINE      CODE     ---       #604
      0100048EH   LINE      CODE     ---       #606
      0100048EH   LINE      CODE     ---       #607
      01000490H   LINE      CODE     ---       #608
      01000490H   LINE      CODE     ---       #609
      01000490H   LINE      CODE     ---       #610
      01000495H   LINE      CODE     ---       #611
      01000495H   LINE      CODE     ---       #612
      01000495H   LINE      CODE     ---       #613
      01000495H   LINE      CODE     ---       #614
      ---         BLOCKEND  ---      ---       LVL=0

      0100071EH   BLOCK     CODE     ---       LVL=0
      00000008H   SYMBOL    DATA     ---       buff
      00000005H   SYMBOL    DATA     BYTE      buffHeadAdd
      0000000CH   SYMBOL    DATA     BYTE      Len
      01000724H   BLOCK     CODE     NEAR LAB  LVL=1
      00000007H   SYMBOL    DATA     BYTE      temp
      00000006H   SYMBOL    DATA     BYTE      i
      ---         BLOCKEND  ---      ---       LVL=1
      0100071EH   LINE      CODE     ---       #618
      01000724H   LINE      CODE     ---       #619
      01000724H   LINE      CODE     ---       #622
      01000726H   LINE      CODE     ---       #623
      01000732H   LINE      CODE     ---       #624
      01000732H   LINE      CODE     ---       #625
      01000742H   LINE      CODE     ---       #626
      01000745H   LINE      CODE     ---       #627
      01000745H   LINE      CODE     ---       #628
      ---         BLOCKEND  ---      ---       LVL=0

      010007E5H   BLOCK     CODE     ---       LVL=0
      010007E5H   LINE      CODE     ---       #671
      010007E5H   LINE      CODE     ---       #672
      010007E5H   LINE      CODE     ---       #673
      010007E8H   LINE      CODE     ---       #674
      010007E8H   LINE      CODE     ---       #675
LX51 LINKER/LOCATER V4.66.93.0                                                        06/03/2025  11:44:05  PAGE 16


      010007EAH   LINE      CODE     ---       #676
      010007EDH   LINE      CODE     ---       #679
      010007F5H   LINE      CODE     ---       #680
      010007FBH   LINE      CODE     ---       #682
      010007FDH   LINE      CODE     ---       #683
      010007FDH   LINE      CODE     ---       #684
      ---         BLOCKEND  ---      ---       LVL=0

      010005DAH   BLOCK     CODE     ---       LVL=0
      010005DAH   BLOCK     CODE     NEAR LAB  LVL=1
      00000007H   SYMBOL    DATA     BYTE      i
      ---         BLOCKEND  ---      ---       LVL=1
      010005DAH   LINE      CODE     ---       #694
      010005DAH   LINE      CODE     ---       #695
      010005DAH   LINE      CODE     ---       #698
      010005DDH   LINE      CODE     ---       #699
      010005DDH   LINE      CODE     ---       #700
      010005E0H   LINE      CODE     ---       #701
      010005E0H   LINE      CODE     ---       #702
      010005E2H   LINE      CODE     ---       #703
      010005EDH   LINE      CODE     ---       #704
      010005EDH   LINE      CODE     ---       #705
      010005F0H   LINE      CODE     ---       #706
      010005F2H   LINE      CODE     ---       #707
      010005F4H   LINE      CODE     ---       #710
      010005FCH   LINE      CODE     ---       #711
      01000602H   LINE      CODE     ---       #714
      0100060EH   LINE      CODE     ---       #715
      0100060EH   LINE      CODE     ---       #716
      0100061AH   LINE      CODE     ---       #717
      0100061DH   LINE      CODE     ---       #719
      0100061FH   LINE      CODE     ---       #720
      01000624H   LINE      CODE     ---       #721
      01000626H   LINE      CODE     ---       #722
      01000628H   LINE      CODE     ---       #723
      01000628H   LINE      CODE     ---       #724
      01000628H   LINE      CODE     ---       #725
      01000628H   LINE      CODE     ---       #726
      ---         BLOCKEND  ---      ---       LVL=0

      010006ECH   BLOCK     CODE     ---       LVL=0
      010006ECH   LINE      CODE     ---       #732
      010006ECH   LINE      CODE     ---       #733
      010006ECH   LINE      CODE     ---       #734
      010006EEH   LINE      CODE     ---       #735
      010006F1H   LINE      CODE     ---       #736
      010006F4H   LINE      CODE     ---       #737
      010006F7H   LINE      CODE     ---       #738
      010006FAH   LINE      CODE     ---       #739
      01000701H   LINE      CODE     ---       #740
      01000706H   LINE      CODE     ---       #741
      01000709H   LINE      CODE     ---       #746
      0100070BH   LINE      CODE     ---       #747
      0100070EH   LINE      CODE     ---       #749
      01000710H   LINE      CODE     ---       #751
      01000710H   LINE      CODE     ---       #752
      01000710H   LINE      CODE     ---       #753
      01000713H   LINE      CODE     ---       #755
      01000716H   LINE      CODE     ---       #757
      01000719H   LINE      CODE     ---       #758
      0100071CH   LINE      CODE     ---       #759
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       INTERRUPT
      00000013H   PUBLIC    DATA     BYTE      loop_20ms_exit
LX51 LINKER/LOCATER V4.66.93.0                                                        06/03/2025  11:44:05  PAGE 17


      00000020H.5 PUBLIC    BIT      BIT       gbFg10msCH1
      02000047H   PUBLIC    XDATA    BYTE      gbUart0TxdLen
      02000026H   PUBLIC    XDATA    ---       gbUart0Buff1
      00000020H.4 PUBLIC    BIT      BIT       gbFg_Uart0SendRec
      00000020H.3 PUBLIC    BIT      BIT       gbFg5msCH1
      00000012H   PUBLIC    DATA     BYTE      SBUFTemp
      02000025H   PUBLIC    XDATA    BYTE      gbUart0Len
      00000020H.2 PUBLIC    BIT      BIT       gbFg_Uart0Ack
      02000004H   PUBLIC    XDATA    ---       gbUart0Buff
      02000003H   PUBLIC    XDATA    BYTE      gbUart0Count500ms
      00000020H.1 PUBLIC    BIT      BIT       gbFg_Uart1ReOk
      02000002H   PUBLIC    XDATA    BYTE      gbUart0Head
      00000020H.0 PUBLIC    BIT      BIT       gbFg_Uart0RxEnd
      02000001H   PUBLIC    XDATA    BYTE      gb500msCnt485
      02000000H   PUBLIC    XDATA    BYTE      gbUart0RxLen
      0100005EH   PUBLIC    CODE     ---       ELPD_INT
      01000057H   PUBLIC    CODE     ---       PWM_INT
      0100002EH   PUBLIC    CODE     ---       SCM_INT
      0100004EH   PUBLIC    CODE     ---       EX2_INT
      01000026H   PUBLIC    CODE     ---       ADC_INT
      01000016H   PUBLIC    CODE     ---       Timer2_INT
      010002CDH   PUBLIC    CODE     ---       EUART0_INT
      0100000EH   PUBLIC    CODE     ---       Timer1_INT
      01000006H   PUBLIC    CODE     ---       EX1_INT
      0100050AH   PUBLIC    CODE     ---       Timer0_INT
      0100002AH   PUBLIC    CODE     ---       EX0_INT
      0000000DH   PUBLIC    DATA     BYTE      ?_CalCheckSum1?BYTE
      01000746H   PUBLIC    CODE     ---       _CalCheckSum1
      000000EAH   SFRSYM    DATA     BYTE      P1M0
      000000F3H   SFRSYM    DATA     BYTE      IB_CON2
      000000E8H.2 SFRSYM    DATA     BIT       IT20
      000000E2H   SFRSYM    DATA     BYTE      P1M1
      000000F4H   SFRSYM    DATA     BYTE      IB_CON3
      000000E8H.3 SFRSYM    DATA     BIT       IT21
      000000ECH   SFRSYM    DATA     BYTE      P3M0
      00000090H   SFRSYM    DATA     BYTE      P1
      000000F5H   SFRSYM    DATA     BYTE      IB_CON4
      000000EDH   SFRSYM    DATA     BYTE      P4M0
      000000E4H   SFRSYM    DATA     BYTE      P3M1
      000000F6H   SFRSYM    DATA     BYTE      IB_CON5
      00000098H.6 SFRSYM    DATA     BIT       SM1_RXOV
      000000E5H   SFRSYM    DATA     BYTE      P4M1
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000FCH   SFRSYM    DATA     BYTE      IB_DATA
      000000A8H   SFRSYM    DATA     BYTE      IEN0
      000000A8H.6 SFRSYM    DATA     BIT       EADC
      000000A9H   SFRSYM    DATA     BYTE      IEN1
      00000085H   SFRSYM    DATA     BYTE      DPH1
      00000095H   SFRSYM    DATA     BYTE      ADCH
      00000097H   SFRSYM    DATA     BYTE      ADDH
      000000B4H   SFRSYM    DATA     BYTE      IPH0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000B0H.0 SFRSYM    DATA     BIT       P3_0
      00000090H.2 SFRSYM    DATA     BIT       P1_2
      000000B5H   SFRSYM    DATA     BYTE      IPH1
      000000C0H.0 SFRSYM    DATA     BIT       P4_0
      000000B0H.1 SFRSYM    DATA     BIT       P3_1
      00000090H.3 SFRSYM    DATA     BIT       P1_3
      000000E8H   SFRSYM    DATA     BYTE      EXF0
      000000C0H.1 SFRSYM    DATA     BIT       P4_1
      000000B0H.2 SFRSYM    DATA     BIT       P3_2
      00000090H.4 SFRSYM    DATA     BIT       P1_4
LX51 LINKER/LOCATER V4.66.93.0                                                        06/03/2025  11:44:05  PAGE 18


      000000C0H.2 SFRSYM    DATA     BIT       P4_2
      000000B0H.3 SFRSYM    DATA     BIT       P3_3
      00000090H.5 SFRSYM    DATA     BIT       P1_5
      000000C8H.6 SFRSYM    DATA     BIT       EXF2
      00000096H   SFRSYM    DATA     BYTE      ADDL
      000000B8H   SFRSYM    DATA     BYTE      IPL0
      00000090H.6 SFRSYM    DATA     BIT       P1_6
      000000B9H   SFRSYM    DATA     BYTE      IPL1
      00000090H.7 SFRSYM    DATA     BIT       P1_7
      0000008EH   SFRSYM    DATA     BYTE      SUSLO
      000000B0H.7 SFRSYM    DATA     BIT       P3_7
      00000098H.0 SFRSYM    DATA     BIT       RI
      000000D0H.7 SFRSYM    DATA     BIT       CY
      00000098H.1 SFRSYM    DATA     BIT       TI
      000000B8H.1 SFRSYM    DATA     BIT       PT0L
      000000B8H.3 SFRSYM    DATA     BIT       PT1L
      000000CBH   SFRSYM    DATA     BYTE      RCAP2H
      000000B8H.5 SFRSYM    DATA     BIT       PT2L
      00000081H   SFRSYM    DATA     BYTE      SP
      000000B8H.0 SFRSYM    DATA     BIT       PX0L
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000B8H.2 SFRSYM    DATA     BIT       PX1L
      000000CAH   SFRSYM    DATA     BYTE      RCAP2L
      000000C8H.1 SFRSYM    DATA     BIT       C_T2
      000000C8H.5 SFRSYM    DATA     BIT       RCLK
      000000C8H.4 SFRSYM    DATA     BIT       TCLK
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000F1H   SFRSYM    DATA     BYTE      AUXC
      00000098H   SFRSYM    DATA     BYTE      SCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000FBH   SFRSYM    DATA     BYTE      IB_OFFSET
      000000B1H   SFRSYM    DATA     BYTE      RSTSTAT
      000000D3H   SFRSYM    DATA     BYTE      PWMD
      00000098H.7 SFRSYM    DATA     BIT       SM0_FE
      000000B2H   SFRSYM    DATA     BYTE      CLKCON
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      00000098H.5 SFRSYM    DATA     BIT       SM2_TXCOL
      000000E8H.0 SFRSYM    DATA     BIT       IE2
      000000F0H   SFRSYM    DATA     BYTE      B
      000000B3H   SFRSYM    DATA     BYTE      LPDCON
      000000C8H.0 SFRSYM    DATA     BIT       CP_RL2
      000000D2H   SFRSYM    DATA     BYTE      PWMP
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.7 SFRSYM    DATA     BIT       TF1
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.2 SFRSYM    DATA     BIT       RB8
      000000C8H.7 SFRSYM    DATA     BIT       TF2
      0000008CH   SFRSYM    DATA     BYTE      TH0
      00000086H   SFRSYM    DATA     BYTE      INSCON
      00000088H.0 SFRSYM    DATA     BIT       IT0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      00000098H.3 SFRSYM    DATA     BIT       TB8
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      0000008AH   SFRSYM    DATA     BYTE      TL0
LX51 LINKER/LOCATER V4.66.93.0                                                        06/03/2025  11:44:05  PAGE 19


      0000008BH   SFRSYM    DATA     BYTE      TL1
      000000A7H   SFRSYM    DATA     BYTE      FLASHCON
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000D1H   SFRSYM    DATA     BYTE      PWMCON
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000C8H.2 SFRSYM    DATA     BIT       TR2
      00000094H   SFRSYM    DATA     BYTE      ADT
      00000083H   SFRSYM    DATA     BYTE      DPH
      00000082H   SFRSYM    DATA     BYTE      DPL
      000000C8H.3 SFRSYM    DATA     BIT       EXEN2
      00000098H.4 SFRSYM    DATA     BIT       REN
      000000B8H.6 SFRSYM    DATA     BIT       PADCL
      00000093H   SFRSYM    DATA     BYTE      ADCON
      000000CEH   SFRSYM    DATA     BYTE      TCON1
      000000C9H   SFRSYM    DATA     BYTE      T2MOD
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      0000009BH   SFRSYM    DATA     BYTE      SADEN
      000000B8H.4 SFRSYM    DATA     BIT       PSL
      0000009AH   SFRSYM    DATA     BYTE      SADDR
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000D0H.1 SFRSYM    DATA     BIT       F1
      000000F7H   SFRSYM    DATA     BYTE      XPAGE
      000000D0H   SFRSYM    DATA     BYTE      PSW
      000000F2H   SFRSYM    DATA     BYTE      IB_CON1

      01000746H   BLOCK     CODE     ---       LVL=0
      0000000DH   SYMBOL    DATA     ---       buff
      00000005H   SYMBOL    DATA     BYTE      buffHeadAdd
      00000011H   SYMBOL    DATA     BYTE      Len
      0100074CH   BLOCK     CODE     NEAR LAB  LVL=1
      00000007H   SYMBOL    DATA     BYTE      temp
      00000006H   SYMBOL    DATA     BYTE      i
      ---         BLOCKEND  ---      ---       LVL=1
      01000746H   LINE      CODE     ---       #104
      0100074CH   LINE      CODE     ---       #105
      0100074CH   LINE      CODE     ---       #108
      0100074EH   LINE      CODE     ---       #109
      0100075AH   LINE      CODE     ---       #110
      0100075AH   LINE      CODE     ---       #111
      0100076AH   LINE      CODE     ---       #112
      0100076DH   LINE      CODE     ---       #113
      0100076DH   LINE      CODE     ---       #114
      ---         BLOCKEND  ---      ---       LVL=0

      0100002AH   BLOCK     CODE     ---       LVL=0
      0100002AH   LINE      CODE     ---       #119
      0100002AH   LINE      CODE     ---       #122
      ---         BLOCKEND  ---      ---       LVL=0

      0100050AH   BLOCK     CODE     ---       LVL=0
      0100050AH   LINE      CODE     ---       #126
      01000527H   LINE      CODE     ---       #128
      01000529H   LINE      CODE     ---       #129
      0100052BH   LINE      CODE     ---       #130
      0100052EH   LINE      CODE     ---       #131
      01000531H   LINE      CODE     ---       #132
      01000533H   LINE      CODE     ---       #134
      01000535H   LINE      CODE     ---       #135
      01000537H   LINE      CODE     ---       #137
      01000539H   LINE      CODE     ---       #139
      01000547H   LINE      CODE     ---       #140
      01000547H   LINE      CODE     ---       #142
LX51 LINKER/LOCATER V4.66.93.0                                                        06/03/2025  11:44:05  PAGE 20


      01000547H   LINE      CODE     ---       #143
      0100054CH   LINE      CODE     ---       #144
      0100054CH   LINE      CODE     ---       #146
      0100054FH   LINE      CODE     ---       #148
      01000551H   LINE      CODE     ---       #149
      01000553H   LINE      CODE     ---       #151
      01000553H   LINE      CODE     ---       #152
      01000555H   LINE      CODE     ---       #153
      01000555H   LINE      CODE     ---       #155
      01000558H   LINE      CODE     ---       #156
      ---         BLOCKEND  ---      ---       LVL=0

      01000006H   BLOCK     CODE     ---       LVL=0
      01000006H   LINE      CODE     ---       #160
      01000006H   LINE      CODE     ---       #162
      01000008H   LINE      CODE     ---       #163
      0100000AH   LINE      CODE     ---       #165
      ---         BLOCKEND  ---      ---       LVL=0

      0100000EH   BLOCK     CODE     ---       LVL=0
      0100000EH   LINE      CODE     ---       #170
      0100000EH   LINE      CODE     ---       #172
      01000010H   LINE      CODE     ---       #173
      01000012H   LINE      CODE     ---       #175
      ---         BLOCKEND  ---      ---       LVL=0

      010002CDH   BLOCK     CODE     ---       LVL=0
      010002DCH   BLOCK     CODE     NEAR LAB  LVL=1
      00000007H   SYMBOL    DATA     BYTE      i
      00000014H   SYMBOL    DATA     BYTE      checksum
      ---         BLOCKEND  ---      ---       LVL=1
      010002CDH   LINE      CODE     ---       #181
      010002DCH   LINE      CODE     ---       #183
      010002DFH   LINE      CODE     ---       #185
      010002E2H   LINE      CODE     ---       #187
      010002E5H   LINE      CODE     ---       #188
      010002E5H   LINE      CODE     ---       #189
      010002E7H   LINE      CODE     ---       #191
      010002EAH   LINE      CODE     ---       #192
      010002EAH   LINE      CODE     ---       #193
      010002F8H   LINE      CODE     ---       #194
      010002F8H   LINE      CODE     ---       #195
      01000303H   LINE      CODE     ---       #196
      01000307H   LINE      CODE     ---       #197
      01000309H   LINE      CODE     ---       #198
      0100030FH   LINE      CODE     ---       #199
      01000311H   LINE      CODE     ---       #201
      01000311H   LINE      CODE     ---       #202
      0100031DH   LINE      CODE     ---       #203
      0100031DH   LINE      CODE     ---       #204
      01000329H   LINE      CODE     ---       #205
      0100032CH   LINE      CODE     ---       #206
      01000331H   LINE      CODE     ---       #207
      01000335H   LINE      CODE     ---       #208
      01000337H   LINE      CODE     ---       #209
      01000339H   LINE      CODE     ---       #212
      0100033CH   LINE      CODE     ---       #214
      0100033CH   LINE      CODE     ---       #215
      0100033CH   LINE      CODE     ---       #216
      0100033CH   LINE      CODE     ---       #218
      0100033FH   LINE      CODE     ---       #219
      0100033FH   LINE      CODE     ---       #220
      01000341H   LINE      CODE     ---       #222
      01000344H   LINE      CODE     ---       #223
      01000344H   LINE      CODE     ---       #224
LX51 LINKER/LOCATER V4.66.93.0                                                        06/03/2025  11:44:05  PAGE 21


      01000346H   LINE      CODE     ---       #225
      0100034EH   LINE      CODE     ---       #226
      0100034EH   LINE      CODE     ---       #227
      01000353H   LINE      CODE     ---       #229
      01000355H   LINE      CODE     ---       #231
      01000358H   LINE      CODE     ---       #236
      0100035EH   LINE      CODE     ---       #237
      01000364H   LINE      CODE     ---       #238
      01000366H   LINE      CODE     ---       #239
      01000368H   LINE      CODE     ---       #240
      0100036AH   LINE      CODE     ---       #242
      0100036AH   LINE      CODE     ---       #243
      0100036FH   LINE      CODE     ---       #244
      01000373H   LINE      CODE     ---       #245
      01000375H   LINE      CODE     ---       #246
      01000377H   LINE      CODE     ---       #247
      0100037BH   LINE      CODE     ---       #248
      0100037DH   LINE      CODE     ---       #249
      0100037DH   LINE      CODE     ---       #250
      0100037DH   LINE      CODE     ---       #251
      0100037DH   LINE      CODE     ---       #252
      ---         BLOCKEND  ---      ---       LVL=0

      01000016H   BLOCK     CODE     ---       LVL=0
      01000016H   LINE      CODE     ---       #256
      01000016H   LINE      CODE     ---       #258
      01000018H   LINE      CODE     ---       #259
      0100001AH   LINE      CODE     ---       #261
      ---         BLOCKEND  ---      ---       LVL=0

      01000026H   BLOCK     CODE     ---       LVL=0
      01000026H   LINE      CODE     ---       #265
      01000026H   LINE      CODE     ---       #267
      01000029H   LINE      CODE     ---       #268
      ---         BLOCKEND  ---      ---       LVL=0

      0100004EH   BLOCK     CODE     ---       LVL=0
      0100004EH   LINE      CODE     ---       #272
      0100004EH   LINE      CODE     ---       #274
      01000051H   LINE      CODE     ---       #275
      01000054H   LINE      CODE     ---       #276
      01000056H   LINE      CODE     ---       #278
      ---         BLOCKEND  ---      ---       LVL=0

      0100002EH   BLOCK     CODE     ---       LVL=0
      0100002EH   LINE      CODE     ---       #285
      0100002EH   LINE      CODE     ---       #287
      01000031H   LINE      CODE     ---       #289
      ---         BLOCKEND  ---      ---       LVL=0

      01000057H   BLOCK     CODE     ---       LVL=0
      01000057H   LINE      CODE     ---       #293
      01000057H   LINE      CODE     ---       #295
      0100005AH   LINE      CODE     ---       #297
      ---         BLOCKEND  ---      ---       LVL=0

      0100005EH   BLOCK     CODE     ---       LVL=0
      0100005EH   LINE      CODE     ---       #301
      0100005EH   LINE      CODE     ---       #303
      01000061H   LINE      CODE     ---       #305
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       UARTDRIVER
      010007FEH   PUBLIC    CODE     ---       _UartConf
      01000816H   PUBLIC    CODE     ---       UartIntEnable
LX51 LINKER/LOCATER V4.66.93.0                                                        06/03/2025  11:44:05  PAGE 22


      0100006DH   PUBLIC    CODE     ---       UartIntDisable
      01000066H   PUBLIC    CODE     ---       UARTSendOn
      0100001EH   PUBLIC    CODE     ---       UARTReceiveOn
      010006B0H   PUBLIC    CODE     ---       _UartBaudSet
      01000036H   PUBLIC    CODE     ---       UartInit
      000000EAH   SFRSYM    DATA     BYTE      P1M0
      000000F3H   SFRSYM    DATA     BYTE      IB_CON2
      000000E8H.2 SFRSYM    DATA     BIT       IT20
      000000E2H   SFRSYM    DATA     BYTE      P1M1
      000000F4H   SFRSYM    DATA     BYTE      IB_CON3
      000000E8H.3 SFRSYM    DATA     BIT       IT21
      000000ECH   SFRSYM    DATA     BYTE      P3M0
      00000090H   SFRSYM    DATA     BYTE      P1
      000000F5H   SFRSYM    DATA     BYTE      IB_CON4
      000000EDH   SFRSYM    DATA     BYTE      P4M0
      000000E4H   SFRSYM    DATA     BYTE      P3M1
      000000F6H   SFRSYM    DATA     BYTE      IB_CON5
      00000098H.6 SFRSYM    DATA     BIT       SM1_RXOV
      000000E5H   SFRSYM    DATA     BYTE      P4M1
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000FCH   SFRSYM    DATA     BYTE      IB_DATA
      000000A8H   SFRSYM    DATA     BYTE      IEN0
      000000A8H.6 SFRSYM    DATA     BIT       EADC
      000000A9H   SFRSYM    DATA     BYTE      IEN1
      00000085H   SFRSYM    DATA     BYTE      DPH1
      00000095H   SFRSYM    DATA     BYTE      ADCH
      00000097H   SFRSYM    DATA     BYTE      ADDH
      000000B4H   SFRSYM    DATA     BYTE      IPH0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000B0H.0 SFRSYM    DATA     BIT       P3_0
      00000090H.2 SFRSYM    DATA     BIT       P1_2
      000000B5H   SFRSYM    DATA     BYTE      IPH1
      000000C0H.0 SFRSYM    DATA     BIT       P4_0
      000000B0H.1 SFRSYM    DATA     BIT       P3_1
      00000090H.3 SFRSYM    DATA     BIT       P1_3
      000000E8H   SFRSYM    DATA     BYTE      EXF0
      000000C0H.1 SFRSYM    DATA     BIT       P4_1
      000000B0H.2 SFRSYM    DATA     BIT       P3_2
      00000090H.4 SFRSYM    DATA     BIT       P1_4
      000000C0H.2 SFRSYM    DATA     BIT       P4_2
      000000B0H.3 SFRSYM    DATA     BIT       P3_3
      00000090H.5 SFRSYM    DATA     BIT       P1_5
      000000C8H.6 SFRSYM    DATA     BIT       EXF2
      00000096H   SFRSYM    DATA     BYTE      ADDL
      000000B8H   SFRSYM    DATA     BYTE      IPL0
      00000090H.6 SFRSYM    DATA     BIT       P1_6
      000000B9H   SFRSYM    DATA     BYTE      IPL1
      00000090H.7 SFRSYM    DATA     BIT       P1_7
      0000008EH   SFRSYM    DATA     BYTE      SUSLO
      000000B0H.7 SFRSYM    DATA     BIT       P3_7
      00000098H.0 SFRSYM    DATA     BIT       RI
      000000D0H.7 SFRSYM    DATA     BIT       CY
      00000098H.1 SFRSYM    DATA     BIT       TI
      000000B8H.1 SFRSYM    DATA     BIT       PT0L
      000000B8H.3 SFRSYM    DATA     BIT       PT1L
      000000CBH   SFRSYM    DATA     BYTE      RCAP2H
      000000B8H.5 SFRSYM    DATA     BIT       PT2L
      00000081H   SFRSYM    DATA     BYTE      SP
      000000B8H.0 SFRSYM    DATA     BIT       PX0L
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000B8H.2 SFRSYM    DATA     BIT       PX1L
      000000CAH   SFRSYM    DATA     BYTE      RCAP2L
LX51 LINKER/LOCATER V4.66.93.0                                                        06/03/2025  11:44:05  PAGE 23


      000000C8H.1 SFRSYM    DATA     BIT       C_T2
      000000C8H.5 SFRSYM    DATA     BIT       RCLK
      000000C8H.4 SFRSYM    DATA     BIT       TCLK
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000F1H   SFRSYM    DATA     BYTE      AUXC
      00000098H   SFRSYM    DATA     BYTE      SCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000FBH   SFRSYM    DATA     BYTE      IB_OFFSET
      000000B1H   SFRSYM    DATA     BYTE      RSTSTAT
      000000D3H   SFRSYM    DATA     BYTE      PWMD
      00000098H.7 SFRSYM    DATA     BIT       SM0_FE
      000000B2H   SFRSYM    DATA     BYTE      CLKCON
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      00000098H.5 SFRSYM    DATA     BIT       SM2_TXCOL
      000000E8H.0 SFRSYM    DATA     BIT       IE2
      000000F0H   SFRSYM    DATA     BYTE      B
      000000B3H   SFRSYM    DATA     BYTE      LPDCON
      000000C8H.0 SFRSYM    DATA     BIT       CP_RL2
      000000D2H   SFRSYM    DATA     BYTE      PWMP
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.7 SFRSYM    DATA     BIT       TF1
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.2 SFRSYM    DATA     BIT       RB8
      000000C8H.7 SFRSYM    DATA     BIT       TF2
      0000008CH   SFRSYM    DATA     BYTE      TH0
      00000086H   SFRSYM    DATA     BYTE      INSCON
      00000088H.0 SFRSYM    DATA     BIT       IT0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      00000098H.3 SFRSYM    DATA     BIT       TB8
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      000000A7H   SFRSYM    DATA     BYTE      FLASHCON
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000D1H   SFRSYM    DATA     BYTE      PWMCON
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000C8H.2 SFRSYM    DATA     BIT       TR2
      00000094H   SFRSYM    DATA     BYTE      ADT
      00000083H   SFRSYM    DATA     BYTE      DPH
      00000082H   SFRSYM    DATA     BYTE      DPL
      000000C8H.3 SFRSYM    DATA     BIT       EXEN2
      00000098H.4 SFRSYM    DATA     BIT       REN
      000000B8H.6 SFRSYM    DATA     BIT       PADCL
      00000093H   SFRSYM    DATA     BYTE      ADCON
      000000CEH   SFRSYM    DATA     BYTE      TCON1
      000000C9H   SFRSYM    DATA     BYTE      T2MOD
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      0000009BH   SFRSYM    DATA     BYTE      SADEN
      000000B8H.4 SFRSYM    DATA     BIT       PSL
      0000009AH   SFRSYM    DATA     BYTE      SADDR
      000000D0H.5 SFRSYM    DATA     BIT       F0
LX51 LINKER/LOCATER V4.66.93.0                                                        06/03/2025  11:44:05  PAGE 24


      000000D0H.1 SFRSYM    DATA     BIT       F1
      000000F7H   SFRSYM    DATA     BYTE      XPAGE
      000000D0H   SFRSYM    DATA     BYTE      PSW
      000000F2H   SFRSYM    DATA     BYTE      IB_CON1

      01000036H   BLOCK     CODE     ---       LVL=0
      01000036H   LINE      CODE     ---       #18
      01000036H   LINE      CODE     ---       #19
      01000036H   LINE      CODE     ---       #20
      01000039H   LINE      CODE     ---       #21
      0100003CH   LINE      CODE     ---       #23
      0100003FH   LINE      CODE     ---       #24
      01000042H   LINE      CODE     ---       #25
      01000045H   LINE      CODE     ---       #28
      01000048H   LINE      CODE     ---       #31
      ---         BLOCKEND  ---      ---       LVL=0

      010006B0H   BLOCK     CODE     ---       LVL=0
      00000006H   SYMBOL    DATA     WORD      baudrate
      010006B0H   BLOCK     CODE     NEAR LAB  LVL=1
      0200005AH   SYMBOL    XDATA    WORD      iBaudRate
      ---         BLOCKEND  ---      ---       LVL=1
      010006B0H   LINE      CODE     ---       #34
      010006B0H   LINE      CODE     ---       #35
      010006B0H   LINE      CODE     ---       #38
      010006CEH   LINE      CODE     ---       #39
      010006D1H   LINE      CODE     ---       #40
      010006D4H   LINE      CODE     ---       #41
      010006D6H   LINE      CODE     ---       #42
      010006D8H   LINE      CODE     ---       #43
      010006DBH   LINE      CODE     ---       #44
      010006DEH   LINE      CODE     ---       #45
      010006E1H   LINE      CODE     ---       #46
      010006E3H   LINE      CODE     ---       #48
      010006E6H   LINE      CODE     ---       #50
      010006E9H   LINE      CODE     ---       #51
      010006EBH   LINE      CODE     ---       #52
      ---         BLOCKEND  ---      ---       LVL=0

      0100001EH   BLOCK     CODE     ---       LVL=0
      0100001EH   LINE      CODE     ---       #55
      0100001EH   LINE      CODE     ---       #56
      0100001EH   LINE      CODE     ---       #57
      01000020H   LINE      CODE     ---       #58
      01000022H   LINE      CODE     ---       #59
      ---         BLOCKEND  ---      ---       LVL=0

      01000066H   BLOCK     CODE     ---       LVL=0
      01000066H   LINE      CODE     ---       #62
      01000066H   LINE      CODE     ---       #63
      01000066H   LINE      CODE     ---       #64
      01000068H   LINE      CODE     ---       #65
      0100006AH   LINE      CODE     ---       #66
      0100006CH   LINE      CODE     ---       #67
      ---         BLOCKEND  ---      ---       LVL=0

      0100006DH   BLOCK     CODE     ---       LVL=0
      0100006DH   LINE      CODE     ---       #70
      0100006DH   LINE      CODE     ---       #71
      0100006DH   LINE      CODE     ---       #72
      01000070H   LINE      CODE     ---       #73
      ---         BLOCKEND  ---      ---       LVL=0

      01000816H   BLOCK     CODE     ---       LVL=0
      01000816H   LINE      CODE     ---       #77
LX51 LINKER/LOCATER V4.66.93.0                                                        06/03/2025  11:44:05  PAGE 25


      01000816H   LINE      CODE     ---       #78
      01000816H   LINE      CODE     ---       #79
      01000819H   LINE      CODE     ---       #80
      ---         BLOCKEND  ---      ---       LVL=0

      010007FEH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      state
      010007FEH   LINE      CODE     ---       #86
      010007FEH   LINE      CODE     ---       #87
      010007FEH   LINE      CODE     ---       #88
      01000801H   LINE      CODE     ---       #90
      01000807H   LINE      CODE     ---       #91
      01000807H   LINE      CODE     ---       #92
      01000807H   LINE      CODE     ---       #93
      01000809H   LINE      CODE     ---       #94
      0100080FH   LINE      CODE     ---       #95
      0100080FH   LINE      CODE     ---       #96
      01000812H   LINE      CODE     ---       #97
      01000812H   LINE      CODE     ---       #99
      01000812H   LINE      CODE     ---       #100
      01000815H   LINE      CODE     ---       #101
      01000815H   LINE      CODE     ---       #102
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       ?C_INIT
      010003D1H   PUBLIC    CODE     ---       ?C_START

      ---         MODULE    ---      ---       ?C?CLDOPTR
      010001AFH   PUBLIC    CODE     ---       ?C?CLDOPTR

      ---         MODULE    ---      ---       ?C?CSTPTR
      010001DCH   PUBLIC    CODE     ---       ?C?CSTPTR

      ---         MODULE    ---      ---       ?C?SLDIV
      01000629H   PUBLIC    CODE     ---       ?C?SLDIV

      ---         MODULE    ---      ---       ?C?ULCMP
      010001EEH   PUBLIC    CODE     ---       ?C?ULCMP

      ---         MODULE    ---      ---       ?C?ULDIV
      0100023BH   PUBLIC    CODE     ---       ?C?ULDIV



*** WARNING L57: UNCALLED FUNCTION, IGNORED FOR OVERLAY PROCESS
    NAME:    _CALCHECKSUM/MAIN

*** WARNING L57: UNCALLED FUNCTION, IGNORED FOR OVERLAY PROCESS
    NAME:    _CALCHECKSUM1/INTERRUPT

Program Size: data=56.3 xdata=92 const=0 code=2068
LX51 RUN COMPLETE.  2 WARNING(S),  0 ERROR(S)
