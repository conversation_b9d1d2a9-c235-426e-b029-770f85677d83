LX51 LINKER/LOCATER V4.66.93.0                                                          06/03/2025  13:41:17  PAGE 1


LX51 LINKER/LOCATER V4.66.93.0, INVOKED BY:
C:\KEIL_V5\C51\BIN\LX51.EXE STARTUP.obj, main.obj, interrupt.obj, UartDriver.obj TO SH79F083_UART


CPU MODE:     8051 MODE
MEMORY MODEL: SMALL


INPUT MODULES INCLUDED:
  STARTUP.obj (?C_STARTUP)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  main.obj (MAIN)
         COMMENT TYPE 0: C51 V9.59.0.0
  interrupt.obj (INTERRUPT)
         COMMENT TYPE 0: C51 V9.59.0.0
  UartDriver.obj (UARTDRIVER)
         COMMENT TYPE 0: C51 V9.59.0.0
  C:\KEIL_V5\C51\LIB\C51S.LIB (?C_INIT)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL_V5\C51\LIB\C51S.LIB (?C?CLDOPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL_V5\C51\LIB\C51S.LIB (?C?CSTPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL_V5\C51\LIB\C51S.LIB (?C?SLDIV)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL_V5\C51\LIB\C51S.LIB (?C?ULCMP)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL_V5\C51\LIB\C51S.LIB (?C?ULDIV)
         COMMENT TYPE 1: A51 / ASM51 Assembler


ACTIVE MEMORY CLASSES OF MODULE:  SH79F083_UART (?C_STARTUP)

BASE        START       END         USED      MEMORY CLASS
==========================================================
C:000000H   C:000000H   C:00FFFFH   00097AH   CODE
I:000000H   I:000000H   I:0000FFH   000001H   IDATA
I:000000H   I:000000H   I:00007FH   000037H   DATA
X:000000H   X:000000H   X:00FFFFH   00005DH   XDATA
I:000020H.0 I:000020H.0 I:00002FH.7 000001H.3 BIT


MEMORY MAP OF MODULE:  SH79F083_UART (?C_STARTUP)


START     STOP      LENGTH    ALIGN  RELOC    MEMORY CLASS   SEGMENT NAME
=========================================================================

* * * * * * * * * * *   D A T A   M E M O R Y   * * * * * * * * * * * * *
000000H   000007H   000008H   ---    AT..     DATA           "REG BANK 0"
000008H   000013H   00000CH   BYTE   UNIT     DATA           _DATA_GROUP_
000014H   000015H   000002H   BYTE   UNIT     DATA           ?DT?INTERRUPT
000016H.0 00001FH.7 00000AH.0 ---    ---      **GAP**
000020H.0 000020H.5 000000H.6 BIT    UNIT     BIT            ?BI?INTERRUPT
000020H.6 000021H.2 000000H.5 BIT    UNIT     BIT            ?BI?MAIN
000021H.3 000021H   000000H.5 ---    ---      **GAP**
000022H   000042H   000021H   BYTE   UNIT     DATA           ?DT?MAIN
000043H   000043H   000001H   BYTE   UNIT     IDATA          ?STACK

* * * * * * * * * * *   C O D E   M E M O R Y   * * * * * * * * * * * * *
000000H   000002H   000003H   ---    OFFS..   CODE           ?CO??C_STARTUP?0
000003H   000005H   000003H   BYTE   OFFS..   CODE           ?INTERRUPT?00003
000006H   00000AH   000005H   BYTE   UNIT     CODE           ?PR?EX1_INT?INTERRUPT
00000BH   00000DH   000003H   BYTE   OFFS..   CODE           ?INTERRUPT?0000B
00000EH   000012H   000005H   BYTE   UNIT     CODE           ?PR?TIMER1_INT?INTERRUPT
000013H   000015H   000003H   BYTE   OFFS..   CODE           ?INTERRUPT?00013
LX51 LINKER/LOCATER V4.66.93.0                                                        06/03/2025  13:41:17  PAGE 2


000016H   00001AH   000005H   BYTE   UNIT     CODE           ?PR?TIMER2_INT?INTERRUPT
00001BH   00001DH   000003H   BYTE   OFFS..   CODE           ?INTERRUPT?0001B
00001EH   000022H   000005H   BYTE   UNIT     CODE           ?PR?UARTRECEIVEON?UARTDRIVER
000023H   000025H   000003H   BYTE   OFFS..   CODE           ?INTERRUPT?00023
000026H   000029H   000004H   BYTE   UNIT     CODE           ?PR?ADC_INT?INTERRUPT
00002AH   00002AH   000001H   BYTE   UNIT     CODE           ?PR?EX0_INT?INTERRUPT
00002BH   00002DH   000003H   BYTE   OFFS..   CODE           ?INTERRUPT?0002B
00002EH   000031H   000004H   BYTE   UNIT     CODE           ?PR?SCM_INT?INTERRUPT
000032H   000032H   000001H   ---    ---      **GAP**
000033H   000035H   000003H   BYTE   OFFS..   CODE           ?INTERRUPT?00033
000036H   000048H   000013H   BYTE   UNIT     CODE           ?PR?UARTINIT?UARTDRIVER
000049H   00004AH   000002H   ---    ---      **GAP**
00004BH   00004DH   000003H   BYTE   OFFS..   CODE           ?INTERRUPT?0004B
00004EH   000056H   000009H   BYTE   UNIT     CODE           ?PR?EX2_INT?INTERRUPT
000057H   00005AH   000004H   BYTE   UNIT     CODE           ?PR?PWM_INT?INTERRUPT
00005BH   00005DH   000003H   BYTE   OFFS..   CODE           ?INTERRUPT?0005B
00005EH   000061H   000004H   BYTE   UNIT     CODE           ?PR?ELPD_INT?INTERRUPT
000062H   000062H   000001H   ---    ---      **GAP**
000063H   000065H   000003H   BYTE   OFFS..   CODE           ?INTERRUPT?00063
000066H   00006CH   000007H   BYTE   UNIT     CODE           ?PR?UARTSENDON?UARTDRIVER
00006DH   000070H   000004H   BYTE   UNIT     CODE           ?PR?UARTINTDISABLE?UARTDRIVER
000071H   000072H   000002H   ---    ---      **GAP**
000073H   000075H   000003H   BYTE   OFFS..   CODE           ?INTERRUPT?00073
000076H   00024BH   0001D6H   BYTE   UNIT     CODE           ?PR?EUART0_INT?INTERRUPT
00024CH   000384H   000139H   BYTE   UNIT     CODE           ?PR?LEDPROC?MAIN
000385H   0004A2H   00011EH   BYTE   UNIT     CODE           ?C?LIB_CODE
0004A3H   00052EH   00008CH   BYTE   UNIT     CODE           ?C_C51STARTUP
00052FH   0005AEH   000080H   BYTE   UNIT     CODE           ?PR?SCANBUTTON?MAIN
0005AFH   000622H   000074H   BYTE   UNIT     CODE           ?PR?USEDATAINIT?MAIN
000623H   000692H   000070H   BYTE   UNIT     CODE           ?PR?UART0ACK?MAIN
000693H   0006FBH   000069H   BYTE   UNIT     CODE           ?PR?TIMER0_INT?INTERRUPT
0006FCH   000762H   000067H   BYTE   UNIT     CODE           ?C_INITSEG
000763H   0007A6H   000044H   BYTE   UNIT     CODE           ?C?LDIV
0007A7H   0007E9H   000043H   BYTE   UNIT     CODE           ?PR?SYSINIT?MAIN
0007EAH   000827H   00003EH   BYTE   UNIT     CODE           ?PR?_CALCHECKSUM1?INTERRUPT
000828H   000863H   00003CH   BYTE   UNIT     CODE           ?PR?_UARTBAUDSET?UARTDRIVER
000864H   000898H   000035H   BYTE   UNIT     CODE           ?PR?UART0DECODE?MAIN
000899H   0008C4H   00002CH   BYTE   UNIT     CODE           ?PR?MAIN?MAIN
0008C5H   0008ECH   000028H   BYTE   UNIT     CODE           ?PR?_CALCHECKSUM?MAIN
0008EDH   00090FH   000023H   BYTE   UNIT     CODE           ?PR?INITLEDTESTMODE?MAIN
000910H   00092FH   000020H   BYTE   UNIT     CODE           ?PR?CLR_DATARAM?MAIN
000930H   000949H   00001AH   BYTE   UNIT     CODE           ?PR?T0INITIAL?MAIN
00094AH   000963H   00001AH   BYTE   UNIT     CODE           ?PR?T1INITIAL?MAIN
000964H   00097BH   000018H   BYTE   UNIT     CODE           ?PR?_UARTCONF?UARTDRIVER
00097CH   00097FH   000004H   BYTE   UNIT     CODE           ?PR?UARTINTENABLE?UARTDRIVER

* * * * * * * * * * *  X D A T A   M E M O R Y  * * * * * * * * * * * * *
000000H   000047H   000048H   BYTE   UNIT     XDATA          ?XD?INTERRUPT
000048H   000059H   000012H   BYTE   UNIT     XDATA          ?XD?MAIN
00005AH   00005BH   000002H   BYTE   UNIT     XDATA          _XDATA_GROUP_
00005CH   00005CH   000001H   BYTE   UNIT     XDATA          ?XD?SCANBUTTON?MAIN



OVERLAY MAP OF MODULE:   SH79F083_UART (?C_STARTUP)


FUNCTION/MODULE                DATA_GROUP   XDATA_GROUP
--> CALLED FUNCTION/MODULE     START  STOP  START  STOP
=======================================================
?C_C51STARTUP                  ----- -----  ----- -----
  +--> MAIN/MAIN
  +--> ?C_INITSEG

MAIN/MAIN                      ----- -----  ----- -----
LX51 LINKER/LOCATER V4.66.93.0                                                        06/03/2025  13:41:17  PAGE 3


  +--> UARTINTDISABLE/UARTDRIVER
  +--> USEDATAINIT/MAIN
  +--> SYSINIT/MAIN
  +--> UARTINIT/UARTDRIVER
  +--> _UARTBAUDSET/UARTDRIVER
  +--> _UARTCONF/UARTDRIVER
  +--> UARTINTENABLE/UARTDRIVER
  +--> UART0DECODE/MAIN
  +--> UART0ACK/MAIN

UARTINTDISABLE/UARTDRIVER      ----- -----  ----- -----

USEDATAINIT/MAIN               ----- -----  ----- -----

SYSINIT/MAIN                   ----- -----  ----- -----
  +--> T0INITIAL/MAIN
  +--> T1INITIAL/MAIN
  +--> CLR_DATARAM/MAIN

T0INITIAL/MAIN                 ----- -----  ----- -----

T1INITIAL/MAIN                 ----- -----  ----- -----

CLR_DATARAM/MAIN               ----- -----  ----- -----

UARTINIT/UARTDRIVER            ----- -----  ----- -----

_UARTBAUDSET/UARTDRIVER        ----- -----  005AH 005BH

_UARTCONF/UARTDRIVER           ----- -----  ----- -----
  +--> UARTINIT/UARTDRIVER
  +--> UARTSENDON/UARTDRIVER
  +--> UARTRECEIVEON/UARTDRIVER

UARTSENDON/UARTDRIVER          ----- -----  ----- -----

UARTRECEIVEON/UARTDRIVER       ----- -----  ----- -----

UARTINTENABLE/UARTDRIVER       ----- -----  ----- -----

UART0DECODE/MAIN               ----- -----  ----- -----
  +--> _CALCHECKSUM/MAIN

_CALCHECKSUM/MAIN              0008H 000CH  ----- -----

UART0ACK/MAIN                  ----- -----  ----- -----

?C_INITSEG                     ----- -----  ----- -----

*** NEW ROOT *****************

EX0_INT/INTERRUPT              ----- -----  ----- -----

*** NEW ROOT *****************

TIMER0_INT/INTERRUPT           ----- -----  ----- -----
  +--> LEDPROC/MAIN

LEDPROC/MAIN                   ----- -----  ----- -----

*** NEW ROOT *****************

EX1_INT/INTERRUPT              ----- -----  ----- -----

*** NEW ROOT *****************
LX51 LINKER/LOCATER V4.66.93.0                                                        06/03/2025  13:41:17  PAGE 4



TIMER1_INT/INTERRUPT           ----- -----  ----- -----

*** NEW ROOT *****************

EUART0_INT/INTERRUPT           000DH 000EH  ----- -----
  +--> _CALCHECKSUM1/INTERRUPT

_CALCHECKSUM1/INTERRUPT        000FH 0013H  ----- -----

*** NEW ROOT *****************

TIMER2_INT/INTERRUPT           ----- -----  ----- -----

*** NEW ROOT *****************

ADC_INT/INTERRUPT              ----- -----  ----- -----

*** NEW ROOT *****************

EX2_INT/INTERRUPT              ----- -----  ----- -----

*** NEW ROOT *****************

SCM_INT/INTERRUPT              ----- -----  ----- -----

*** NEW ROOT *****************

PWM_INT/INTERRUPT              ----- -----  ----- -----

*** NEW ROOT *****************

ELPD_INT/INTERRUPT             ----- -----  ----- -----



PUBLIC SYMBOLS OF MODULE:  SH79F083_UART (?C_STARTUP)


      VALUE       CLASS    TYPE      PUBLIC SYMBOL NAME
      =================================================
      0000000FH   DATA     BYTE      ?_CalCheckSum1?BYTE
      00000008H   DATA     BYTE      ?_CalCheckSum?BYTE
      01000385H   CODE     ---       ?C?CLDOPTR
      00000000H   NUMBER   ---       ?C?CODESEG
      010003B2H   CODE     ---       ?C?CSTPTR
      01000763H   CODE     ---       ?C?SLDIV
      010003C4H   CODE     ---       ?C?ULCMP
      01000411H   CODE     ---       ?C?ULDIV
      00000000H   NUMBER   ---       ?C?XDATASEG
      010004EAH   CODE     ---       ?C_START
      01000000H   CODE     ---       ?C_STARTUP
      010008C5H   CODE     ---       _CalCheckSum
      01000800H   CODE     ---       _CalCheckSum1
      01000828H   CODE     ---       _UartBaudSet
      01000964H   CODE     ---       _UartConf
*SFR* 000000D0H.6 DATA     BIT       AC
*SFR* 000000E0H   DATA     BYTE      ACC
      01000026H   CODE     ---       ADC_INT
*SFR* 00000095H   DATA     BYTE      ADCH
*SFR* 00000093H   DATA     BYTE      ADCON
*SFR* 00000097H   DATA     BYTE      ADDH
*SFR* 00000096H   DATA     BYTE      ADDL
*SFR* 00000094H   DATA     BYTE      ADT
*SFR* 000000F1H   DATA     BYTE      AUXC
LX51 LINKER/LOCATER V4.66.93.0                                                        06/03/2025  13:41:17  PAGE 5


*SFR* 000000F0H   DATA     BYTE      B
*SFR* 000000B0H.2 DATA     BIT       BUTTON
      02000054H   XDATA    BYTE      BUTTON_Buff
      02000052H   XDATA    BYTE      BUTTON_Index
      02000057H   XDATA    BYTE      BUTTON_New
      02000055H   XDATA    BYTE      BUTTON_Old
      02000053H   XDATA    BYTE      BUTTON_Value
      02000058H   XDATA    BYTE      BUTTON_Value1
      0200004DH   XDATA    ---       BUTTON_Value_Buff
      0200004AH   XDATA    BYTE      ButtonID
*SFR* 000000C8H.1 DATA     BIT       C_T2
      02000056H   XDATA    BYTE      Check
*SFR* 000000B2H   DATA     BYTE      CLKCON
      01000910H   CODE     ---       CLR_DataRAM
*SFR* 000000C8H.0 DATA     BIT       CP_RL2
*SFR* 000000D0H.7 DATA     BIT       CY
*SFR* 00000083H   DATA     BYTE      DPH
*SFR* 00000085H   DATA     BYTE      DPH1
*SFR* 00000082H   DATA     BYTE      DPL
*SFR* 00000084H   DATA     BYTE      DPL1
*SFR* 000000A8H.7 DATA     BIT       EA
*SFR* 000000A8H.6 DATA     BIT       EADC
      0100005EH   CODE     ---       ELPD_INT
*SFR* 000000A8H.4 DATA     BIT       ES0
*SFR* 000000A8H.1 DATA     BIT       ET0
*SFR* 000000A8H.3 DATA     BIT       ET1
*SFR* 000000A8H.5 DATA     BIT       ET2
      01000076H   CODE     ---       EUART0_INT
*SFR* 000000A8H.0 DATA     BIT       EX0
      0100002AH   CODE     ---       EX0_INT
*SFR* 000000A8H.2 DATA     BIT       EX1
      01000006H   CODE     ---       EX1_INT
      0100004EH   CODE     ---       EX2_INT
*SFR* 000000C8H.3 DATA     BIT       EXEN2
*SFR* 000000E8H   DATA     BYTE      EXF0
*SFR* 000000C8H.6 DATA     BIT       EXF2
*SFR* 000000D0H.5 DATA     BIT       F0
*SFR* 000000D0H.1 DATA     BIT       F1
*SFR* 000000A7H   DATA     BYTE      FLASHCON
      02000001H   XDATA    BYTE      gb500msCnt485
      00000021H.2 BIT      BIT       gb_TestMode
      02000048H   XDATA    WORD      gb_TestModeCount3s
      00000032H   DATA     BYTE      gB_Uart0_1ms
      00000020H.5 BIT      BIT       gbFg10msCH1
      00000020H.3 BIT      BIT       gbFg5msCH1
      02000051H   XDATA    BYTE      gBFg_100ms
      00000021H.1 BIT      BIT       gBFg_1ms
      00000020H.7 BIT      BIT       gbFg_BUTTON_Flag
      00000021H.0 BIT      BIT       gBFg_ScanButton_1ms
      00000020H.6 BIT      BIT       gBFg_Uart0_1ms_ack
      00000020H.2 BIT      BIT       gbFg_Uart0Ack
      00000020H.0 BIT      BIT       gbFg_Uart0RxEnd
      00000020H.4 BIT      BIT       gbFg_Uart0SendRec
      00000020H.1 BIT      BIT       gbFg_Uart1ReOk
      02000004H   XDATA    ---       gbUart0Buff
      02000026H   XDATA    ---       gbUart0Buff1
      02000003H   XDATA    BYTE      gbUart0Count500ms
      02000002H   XDATA    BYTE      gbUart0Head
      02000025H   XDATA    BYTE      gbUart0Len
      02000000H   XDATA    BYTE      gbUart0RxLen
      02000047H   XDATA    BYTE      gbUart0TxdLen
*SFR* 000000F2H   DATA     BYTE      IB_CON1
*SFR* 000000F3H   DATA     BYTE      IB_CON2
*SFR* 000000F4H   DATA     BYTE      IB_CON3
*SFR* 000000F5H   DATA     BYTE      IB_CON4
LX51 LINKER/LOCATER V4.66.93.0                                                        06/03/2025  13:41:17  PAGE 6


*SFR* 000000F6H   DATA     BYTE      IB_CON5
*SFR* 000000FCH   DATA     BYTE      IB_DATA
*SFR* 000000FBH   DATA     BYTE      IB_OFFSET
*SFR* 00000088H.1 DATA     BIT       IE0
*SFR* 00000088H.3 DATA     BIT       IE1
*SFR* 000000E8H.0 DATA     BIT       IE2
*SFR* 000000A8H   DATA     BYTE      IEN0
*SFR* 000000A9H   DATA     BYTE      IEN1
      010008EDH   CODE     ---       InitLedTestMode
*SFR* 00000086H   DATA     BYTE      INSCON
*SFR* 000000B4H   DATA     BYTE      IPH0
*SFR* 000000B5H   DATA     BYTE      IPH1
*SFR* 000000B8H   DATA     BYTE      IPL0
*SFR* 000000B9H   DATA     BYTE      IPL1
*SFR* 00000088H.0 DATA     BIT       IT0
*SFR* 00000088H.2 DATA     BIT       IT1
*SFR* 000000E8H.2 DATA     BIT       IT20
*SFR* 000000E8H.3 DATA     BIT       IT21
*SFR* 00000090H.7 DATA     BIT       LED1
      0000003BH   DATA     DWORD     LED1_Count
      00000022H   DATA     WORD      LED1_Off
      00000033H   DATA     DWORD     LED1_Off_Count
      0000002EH   DATA     WORD      LED1_On
      00000026H   DATA     DWORD     LED1_On_Count
*SFR* 00000090H.6 DATA     BIT       LED2
      0000003FH   DATA     DWORD     LED2_Count
      00000024H   DATA     WORD      LED2_Off
      00000037H   DATA     DWORD     LED2_Off_Count
      00000030H   DATA     WORD      LED2_On
      0000002AH   DATA     DWORD     LED2_On_Count
      0100024CH   CODE     ---       LEDProc
      02000059H   XDATA    BYTE      Lenth
      00000015H   DATA     BYTE      loop_20ms_exit
*SFR* 000000B3H   DATA     BYTE      LPDCON
      01000899H   CODE     ---       main
      0200004BH   XDATA    BYTE      OBJ_CMD
*SFR* 000000D0H.2 DATA     BIT       OV
*SFR* 000000D0H.0 DATA     BIT       P
*SFR* 00000090H   DATA     BYTE      P1
*SFR* 00000090H.2 DATA     BIT       P1_2
*SFR* 00000090H.3 DATA     BIT       P1_3
*SFR* 00000090H.4 DATA     BIT       P1_4
*SFR* 00000090H.5 DATA     BIT       P1_5
*SFR* 00000090H.6 DATA     BIT       P1_6
*SFR* 00000090H.7 DATA     BIT       P1_7
*SFR* 000000EAH   DATA     BYTE      P1M0
*SFR* 000000E2H   DATA     BYTE      P1M1
*SFR* 000000B0H   DATA     BYTE      P3
*SFR* 000000B0H.0 DATA     BIT       P3_0
*SFR* 000000B0H.1 DATA     BIT       P3_1
*SFR* 000000B0H.2 DATA     BIT       P3_2
*SFR* 000000B0H.3 DATA     BIT       P3_3
*SFR* 000000B0H.7 DATA     BIT       P3_7
*SFR* 000000ECH   DATA     BYTE      P3M0
*SFR* 000000E4H   DATA     BYTE      P3M1
*SFR* 000000C0H   DATA     BYTE      P4
*SFR* 000000C0H.0 DATA     BIT       P4_0
*SFR* 000000C0H.1 DATA     BIT       P4_1
*SFR* 000000C0H.2 DATA     BIT       P4_2
*SFR* 000000EDH   DATA     BYTE      P4M0
*SFR* 000000E5H   DATA     BYTE      P4M1
*SFR* 000000B8H.6 DATA     BIT       PADCL
*SFR* 00000087H   DATA     BYTE      PCON
*SFR* 000000B8H.4 DATA     BIT       PSL
*SFR* 000000D0H   DATA     BYTE      PSW
LX51 LINKER/LOCATER V4.66.93.0                                                        06/03/2025  13:41:17  PAGE 7


*SFR* 000000B8H.1 DATA     BIT       PT0L
*SFR* 000000B8H.3 DATA     BIT       PT1L
*SFR* 000000B8H.5 DATA     BIT       PT2L
      01000057H   CODE     ---       PWM_INT
*SFR* 000000D1H   DATA     BYTE      PWMCON
*SFR* 000000D3H   DATA     BYTE      PWMD
*SFR* 000000D2H   DATA     BYTE      PWMP
*SFR* 000000B8H.0 DATA     BIT       PX0L
*SFR* 000000B8H.2 DATA     BIT       PX1L
*SFR* 00000098H.2 DATA     BIT       RB8
*SFR* 000000CBH   DATA     BYTE      RCAP2H
*SFR* 000000CAH   DATA     BYTE      RCAP2L
*SFR* 000000C8H.5 DATA     BIT       RCLK
*SFR* 00000098H.4 DATA     BIT       REN
*SFR* 00000098H.0 DATA     BIT       RI
*SFR* 000000D0H.3 DATA     BIT       RS0
*SFR* 000000D0H.4 DATA     BIT       RS1
*SFR* 000000B1H   DATA     BYTE      RSTSTAT
*SFR* 0000009AH   DATA     BYTE      SADDR
*SFR* 0000009BH   DATA     BYTE      SADEN
*SFR* 00000099H   DATA     BYTE      SBUF
      00000014H   DATA     BYTE      SBUFTemp
      0100052FH   CODE     ---       ScanButton
      0100002EH   CODE     ---       SCM_INT
*SFR* 00000098H   DATA     BYTE      SCON
*SFR* 00000098H.7 DATA     BIT       SM0_FE
*SFR* 00000098H.6 DATA     BIT       SM1_RXOV
*SFR* 00000098H.5 DATA     BIT       SM2_TXCOL
*SFR* 00000081H   DATA     BYTE      SP
*SFR* 0000008EH   DATA     BYTE      SUSLO
      010007A7H   CODE     ---       SysInit
      01000930H   CODE     ---       T0Initial
      0100094AH   CODE     ---       T1Initial
*SFR* 000000C8H   DATA     BYTE      T2CON
*SFR* 000000C9H   DATA     BYTE      T2MOD
*SFR* 00000098H.3 DATA     BIT       TB8
*SFR* 000000C8H.4 DATA     BIT       TCLK
*SFR* 00000088H   DATA     BYTE      TCON
*SFR* 000000CEH   DATA     BYTE      TCON1
*SFR* 00000088H.5 DATA     BIT       TF0
*SFR* 00000088H.7 DATA     BIT       TF1
*SFR* 000000C8H.7 DATA     BIT       TF2
*SFR* 0000008CH   DATA     BYTE      TH0
*SFR* 0000008DH   DATA     BYTE      TH1
*SFR* 000000CDH   DATA     BYTE      TH2
*SFR* 00000098H.1 DATA     BIT       TI
      01000693H   CODE     ---       Timer0_INT
      0100000EH   CODE     ---       Timer1_INT
      01000016H   CODE     ---       Timer2_INT
*SFR* 0000008AH   DATA     BYTE      TL0
*SFR* 0000008BH   DATA     BYTE      TL1
*SFR* 000000CCH   DATA     BYTE      TL2
*SFR* 00000089H   DATA     BYTE      TMOD
*SFR* 00000088H.4 DATA     BIT       TR0
*SFR* 00000088H.6 DATA     BIT       TR1
*SFR* 000000C8H.2 DATA     BIT       TR2
      01000623H   CODE     ---       Uart0Ack
      01000864H   CODE     ---       Uart0Decode
      01000036H   CODE     ---       UartInit
      0100006DH   CODE     ---       UartIntDisable
      0100097CH   CODE     ---       UartIntEnable
      0100001EH   CODE     ---       UARTReceiveOn
      01000066H   CODE     ---       UARTSendOn
      0200004CH   XDATA    BYTE      UpOrDown
      010005AFH   CODE     ---       UseDataInit
LX51 LINKER/LOCATER V4.66.93.0                                                        06/03/2025  13:41:17  PAGE 8


*SFR* 000000F7H   DATA     BYTE      XPAGE



SYMBOL TABLE OF MODULE:  SH79F083_UART (?C_STARTUP)

      VALUE       REP       CLASS    TYPE      SYMBOL NAME
      ====================================================
      ---         MODULE    ---      ---       ?C_STARTUP
      01000000H   PUBLIC    CODE     ---       ?C_STARTUP
      000000E0H   SYMBOL    DATA     ---       ACC
      000000F0H   SYMBOL    DATA     ---       B
      00000083H   SYMBOL    DATA     ---       DPH
      00000082H   SYMBOL    DATA     ---       DPL
      00000000H   SYMBOL    NUMBER   ---       IBPSTACK
      00000100H   SYMBOL    NUMBER   ---       IBPSTACKTOP
      00000080H   SYMBOL    NUMBER   ---       IDATALEN
      010004A6H   SYMBOL    CODE     ---       IDATALOOP
      00000000H   SYMBOL    NUMBER   ---       PBPSTACK
      00000100H   SYMBOL    NUMBER   ---       PBPSTACKTOP
      00000000H   SYMBOL    NUMBER   ---       PDATALEN
      00000000H   SYMBOL    NUMBER   ---       PDATASTART
      00000000H   SYMBOL    NUMBER   ---       PPAGE
      00000000H   SYMBOL    NUMBER   ---       PPAGEENABLE
      000000A0H   SYMBOL    DATA     ---       PPAGE_SFR
      00000081H   SYMBOL    DATA     ---       SP
      010004A3H   SYMBOL    CODE     ---       STARTUP1
      00000000H   SYMBOL    NUMBER   ---       XBPSTACK
      00000000H   SYMBOL    NUMBER   ---       XBPSTACKTOP
      00000000H   SYMBOL    NUMBER   ---       XDATALEN
      00000000H   SYMBOL    NUMBER   ---       XDATASTART
      01000000H   LINE      CODE     ---       #126
      010004A3H   LINE      CODE     ---       #133
      010004A5H   LINE      CODE     ---       #134
      010004A6H   LINE      CODE     ---       #135
      010004A7H   LINE      CODE     ---       #136
      010004A9H   LINE      CODE     ---       #185
      010004ACH   LINE      CODE     ---       #196

      ---         MODULE    ---      ---       MAIN
      02000059H   PUBLIC    XDATA    BYTE      Lenth
      0000003FH   PUBLIC    DATA     DWORD     LED2_Count
      0000003BH   PUBLIC    DATA     DWORD     LED1_Count
      00000037H   PUBLIC    DATA     DWORD     LED2_Off_Count
      00000033H   PUBLIC    DATA     DWORD     LED1_Off_Count
      02000058H   PUBLIC    XDATA    BYTE      BUTTON_Value1
      02000057H   PUBLIC    XDATA    BYTE      BUTTON_New
      02000056H   PUBLIC    XDATA    BYTE      Check
      02000055H   PUBLIC    XDATA    BYTE      BUTTON_Old
      00000021H.2 PUBLIC    BIT      BIT       gb_TestMode
      00000032H   PUBLIC    DATA     BYTE      gB_Uart0_1ms
      00000021H.1 PUBLIC    BIT      BIT       gBFg_1ms
      02000054H   PUBLIC    XDATA    BYTE      BUTTON_Buff
      02000053H   PUBLIC    XDATA    BYTE      BUTTON_Value
      02000052H   PUBLIC    XDATA    BYTE      BUTTON_Index
      00000021H.0 PUBLIC    BIT      BIT       gBFg_ScanButton_1ms
      02000051H   PUBLIC    XDATA    BYTE      gBFg_100ms
      00000030H   PUBLIC    DATA     WORD      LED2_On
      0000002EH   PUBLIC    DATA     WORD      LED1_On
      0200004DH   PUBLIC    XDATA    ---       BUTTON_Value_Buff
      0200004CH   PUBLIC    XDATA    BYTE      UpOrDown
      00000020H.7 PUBLIC    BIT      BIT       gbFg_BUTTON_Flag
      0000002AH   PUBLIC    DATA     DWORD     LED2_On_Count
      00000026H   PUBLIC    DATA     DWORD     LED1_On_Count
      0200004BH   PUBLIC    XDATA    BYTE      OBJ_CMD
LX51 LINKER/LOCATER V4.66.93.0                                                        06/03/2025  13:41:17  PAGE 9


      0200004AH   PUBLIC    XDATA    BYTE      ButtonID
      00000020H.6 PUBLIC    BIT      BIT       gBFg_Uart0_1ms_ack
      02000048H   PUBLIC    XDATA    WORD      gb_TestModeCount3s
      00000024H   PUBLIC    DATA     WORD      LED2_Off
      00000022H   PUBLIC    DATA     WORD      LED1_Off
      01000899H   PUBLIC    CODE     ---       main
      01000623H   PUBLIC    CODE     ---       Uart0Ack
      01000864H   PUBLIC    CODE     ---       Uart0Decode
      00000008H   PUBLIC    DATA     BYTE      ?_CalCheckSum?BYTE
      010008C5H   PUBLIC    CODE     ---       _CalCheckSum
      0100052FH   PUBLIC    CODE     ---       ScanButton
      0100024CH   PUBLIC    CODE     ---       LEDProc
      010008EDH   PUBLIC    CODE     ---       InitLedTestMode
      0100094AH   PUBLIC    CODE     ---       T1Initial
      01000930H   PUBLIC    CODE     ---       T0Initial
      010005AFH   PUBLIC    CODE     ---       UseDataInit
      010007A7H   PUBLIC    CODE     ---       SysInit
      01000910H   PUBLIC    CODE     ---       CLR_DataRAM
      000000EAH   SFRSYM    DATA     BYTE      P1M0
      000000F3H   SFRSYM    DATA     BYTE      IB_CON2
      000000E8H.2 SFRSYM    DATA     BIT       IT20
      000000E2H   SFRSYM    DATA     BYTE      P1M1
      000000F4H   SFRSYM    DATA     BYTE      IB_CON3
      000000E8H.3 SFRSYM    DATA     BIT       IT21
      000000ECH   SFRSYM    DATA     BYTE      P3M0
      00000090H   SFRSYM    DATA     BYTE      P1
      000000F5H   SFRSYM    DATA     BYTE      IB_CON4
      000000EDH   SFRSYM    DATA     BYTE      P4M0
      000000E4H   SFRSYM    DATA     BYTE      P3M1
      000000F6H   SFRSYM    DATA     BYTE      IB_CON5
      00000098H.6 SFRSYM    DATA     BIT       SM1_RXOV
      000000E5H   SFRSYM    DATA     BYTE      P4M1
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000A8H.7 SFRSYM    DATA     BIT       EA
      00000090H.7 SFRSYM    DATA     BIT       LED1
      000000FCH   SFRSYM    DATA     BYTE      IB_DATA
      00000090H.6 SFRSYM    DATA     BIT       LED2
      000000A8H   SFRSYM    DATA     BYTE      IEN0
      000000A8H.6 SFRSYM    DATA     BIT       EADC
      000000A9H   SFRSYM    DATA     BYTE      IEN1
      00000085H   SFRSYM    DATA     BYTE      DPH1
      00000095H   SFRSYM    DATA     BYTE      ADCH
      00000097H   SFRSYM    DATA     BYTE      ADDH
      000000B4H   SFRSYM    DATA     BYTE      IPH0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000B0H.0 SFRSYM    DATA     BIT       P3_0
      00000090H.2 SFRSYM    DATA     BIT       P1_2
      000000B5H   SFRSYM    DATA     BYTE      IPH1
      000000C0H.0 SFRSYM    DATA     BIT       P4_0
      000000B0H.1 SFRSYM    DATA     BIT       P3_1
      00000090H.3 SFRSYM    DATA     BIT       P1_3
      000000E8H   SFRSYM    DATA     BYTE      EXF0
      000000C0H.1 SFRSYM    DATA     BIT       P4_1
      000000B0H.2 SFRSYM    DATA     BIT       P3_2
      00000090H.4 SFRSYM    DATA     BIT       P1_4
      000000C0H.2 SFRSYM    DATA     BIT       P4_2
      000000B0H.3 SFRSYM    DATA     BIT       P3_3
      00000090H.5 SFRSYM    DATA     BIT       P1_5
      000000C8H.6 SFRSYM    DATA     BIT       EXF2
      00000096H   SFRSYM    DATA     BYTE      ADDL
      000000B8H   SFRSYM    DATA     BYTE      IPL0
      00000090H.6 SFRSYM    DATA     BIT       P1_6
      000000B9H   SFRSYM    DATA     BYTE      IPL1
LX51 LINKER/LOCATER V4.66.93.0                                                        06/03/2025  13:41:17  PAGE 10


      00000090H.7 SFRSYM    DATA     BIT       P1_7
      0000008EH   SFRSYM    DATA     BYTE      SUSLO
      000000B0H.7 SFRSYM    DATA     BIT       P3_7
      00000098H.0 SFRSYM    DATA     BIT       RI
      000000D0H.7 SFRSYM    DATA     BIT       CY
      00000098H.1 SFRSYM    DATA     BIT       TI
      000000B8H.1 SFRSYM    DATA     BIT       PT0L
      000000B8H.3 SFRSYM    DATA     BIT       PT1L
      000000CBH   SFRSYM    DATA     BYTE      RCAP2H
      000000B8H.5 SFRSYM    DATA     BIT       PT2L
      00000081H   SFRSYM    DATA     BYTE      SP
      000000B8H.0 SFRSYM    DATA     BIT       PX0L
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000B8H.2 SFRSYM    DATA     BIT       PX1L
      000000CAH   SFRSYM    DATA     BYTE      RCAP2L
      000000C8H.1 SFRSYM    DATA     BIT       C_T2
      000000C8H.5 SFRSYM    DATA     BIT       RCLK
      000000C8H.4 SFRSYM    DATA     BIT       TCLK
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000F1H   SFRSYM    DATA     BYTE      AUXC
      00000098H   SFRSYM    DATA     BYTE      SCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000FBH   SFRSYM    DATA     BYTE      IB_OFFSET
      000000B1H   SFRSYM    DATA     BYTE      RSTSTAT
      000000D3H   SFRSYM    DATA     BYTE      PWMD
      00000098H.7 SFRSYM    DATA     BIT       SM0_FE
      000000B2H   SFRSYM    DATA     BYTE      CLKCON
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      00000098H.5 SFRSYM    DATA     BIT       SM2_TXCOL
      000000E8H.0 SFRSYM    DATA     BIT       IE2
      000000F0H   SFRSYM    DATA     BYTE      B
      000000B3H   SFRSYM    DATA     BYTE      LPDCON
      000000C8H.0 SFRSYM    DATA     BIT       CP_RL2
      000000D2H   SFRSYM    DATA     BYTE      PWMP
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.7 SFRSYM    DATA     BIT       TF1
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.2 SFRSYM    DATA     BIT       RB8
      000000C8H.7 SFRSYM    DATA     BIT       TF2
      0000008CH   SFRSYM    DATA     BYTE      TH0
      00000086H   SFRSYM    DATA     BYTE      INSCON
      00000088H.0 SFRSYM    DATA     BIT       IT0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      00000098H.3 SFRSYM    DATA     BIT       TB8
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      000000A7H   SFRSYM    DATA     BYTE      FLASHCON
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000D1H   SFRSYM    DATA     BYTE      PWMCON
      00000088H.6 SFRSYM    DATA     BIT       TR1
LX51 LINKER/LOCATER V4.66.93.0                                                        06/03/2025  13:41:17  PAGE 11


      000000C8H.2 SFRSYM    DATA     BIT       TR2
      00000094H   SFRSYM    DATA     BYTE      ADT
      00000083H   SFRSYM    DATA     BYTE      DPH
      000000B0H.2 SFRSYM    DATA     BIT       BUTTON
      00000082H   SFRSYM    DATA     BYTE      DPL
      000000C8H.3 SFRSYM    DATA     BIT       EXEN2
      00000098H.4 SFRSYM    DATA     BIT       REN
      000000B8H.6 SFRSYM    DATA     BIT       PADCL
      00000093H   SFRSYM    DATA     BYTE      ADCON
      000000CEH   SFRSYM    DATA     BYTE      TCON1
      000000C9H   SFRSYM    DATA     BYTE      T2MOD
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      0000009BH   SFRSYM    DATA     BYTE      SADEN
      000000B8H.4 SFRSYM    DATA     BIT       PSL
      0000009AH   SFRSYM    DATA     BYTE      SADDR
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000D0H.1 SFRSYM    DATA     BIT       F1
      000000F7H   SFRSYM    DATA     BYTE      XPAGE
      000000D0H   SFRSYM    DATA     BYTE      PSW
      000000F2H   SFRSYM    DATA     BYTE      IB_CON1

      01000910H   BLOCK     CODE     ---       LVL=0
      01000910H   BLOCK     CODE     NEAR LAB  LVL=1
      00000001H   SYMBOL    DATA     ---       p
      00000007H   SYMBOL    DATA     BYTE      i
      ---         BLOCKEND  ---      ---       LVL=1
      01000910H   LINE      CODE     ---       #140
      01000910H   LINE      CODE     ---       #141
      01000910H   LINE      CODE     ---       #145
      01000916H   LINE      CODE     ---       #146
      0100092FH   LINE      CODE     ---       #147
      ---         BLOCKEND  ---      ---       LVL=0

      010007A7H   BLOCK     CODE     ---       LVL=0
      010007A7H   LINE      CODE     ---       #162
      010007A7H   LINE      CODE     ---       #163
      010007A7H   LINE      CODE     ---       #164
      010007A9H   LINE      CODE     ---       #166
      010007ACH   LINE      CODE     ---       #167
      010007AFH   LINE      CODE     ---       #168
      010007B1H   LINE      CODE     ---       #170
      010007B4H   LINE      CODE     ---       #171
      010007B7H   LINE      CODE     ---       #173
      010007B9H   LINE      CODE     ---       #174
      010007BCH   LINE      CODE     ---       #176
      010007BEH   LINE      CODE     ---       #177
      010007C1H   LINE      CODE     ---       #180
      010007C3H   LINE      CODE     ---       #187
      010007C6H   LINE      CODE     ---       #188
      010007C9H   LINE      CODE     ---       #189
      010007CBH   LINE      CODE     ---       #190
      010007CDH   LINE      CODE     ---       #194
      010007D0H   LINE      CODE     ---       #197
      010007D3H   LINE      CODE     ---       #204
      010007D6H   LINE      CODE     ---       #208
      010007D9H   LINE      CODE     ---       #211
      010007DBH   LINE      CODE     ---       #213
      010007DEH   LINE      CODE     ---       #216
      010007E1H   LINE      CODE     ---       #217
      010007E4H   LINE      CODE     ---       #219
      010007E7H   LINE      CODE     ---       #221
      ---         BLOCKEND  ---      ---       LVL=0

      010005AFH   BLOCK     CODE     ---       LVL=0
      010005AFH   BLOCK     CODE     NEAR LAB  LVL=1
LX51 LINKER/LOCATER V4.66.93.0                                                        06/03/2025  13:41:17  PAGE 12


      00000007H   SYMBOL    DATA     BYTE      i
      ---         BLOCKEND  ---      ---       LVL=1
      010005AFH   LINE      CODE     ---       #251
      010005AFH   LINE      CODE     ---       #252
      010005AFH   LINE      CODE     ---       #255
      010005B4H   LINE      CODE     ---       #256
      010005B8H   LINE      CODE     ---       #257
      010005BAH   LINE      CODE     ---       #258
      010005BCH   LINE      CODE     ---       #259
      010005C7H   LINE      CODE     ---       #260
      010005C7H   LINE      CODE     ---       #261
      010005D3H   LINE      CODE     ---       #262
      010005D6H   LINE      CODE     ---       #264
      010005DBH   LINE      CODE     ---       #265
      010005DFH   LINE      CODE     ---       #266
      010005E3H   LINE      CODE     ---       #267
      010005E7H   LINE      CODE     ---       #269
      010005E9H   LINE      CODE     ---       #270
      010005EDH   LINE      CODE     ---       #272
      010005F1H   LINE      CODE     ---       #273
      010005F5H   LINE      CODE     ---       #274
      010005F9H   LINE      CODE     ---       #275
      010005FDH   LINE      CODE     ---       #276
      01000601H   LINE      CODE     ---       #277
      01000605H   LINE      CODE     ---       #278
      01000607H   LINE      CODE     ---       #279
      01000609H   LINE      CODE     ---       #280
      0100060BH   LINE      CODE     ---       #282
      01000611H   LINE      CODE     ---       #283
      01000611H   LINE      CODE     ---       #284
      01000611H   LINE      CODE     ---       #285
      01000611H   LINE      CODE     ---       #286
      01000611H   LINE      CODE     ---       #287
      01000611H   LINE      CODE     ---       #288
      01000613H   LINE      CODE     ---       #290
      01000613H   LINE      CODE     ---       #291
      01000616H   LINE      CODE     ---       #292
      0100061AH   LINE      CODE     ---       #293
      0100061EH   LINE      CODE     ---       #294
      01000622H   LINE      CODE     ---       #295
      01000622H   LINE      CODE     ---       #296
      ---         BLOCKEND  ---      ---       LVL=0

      01000930H   BLOCK     CODE     ---       LVL=0
      01000930H   LINE      CODE     ---       #301
      01000930H   LINE      CODE     ---       #302
      01000930H   LINE      CODE     ---       #303
      01000933H   LINE      CODE     ---       #305
      01000936H   LINE      CODE     ---       #306
      01000939H   LINE      CODE     ---       #309
      0100093CH   LINE      CODE     ---       #310
      0100093FH   LINE      CODE     ---       #313
      01000942H   LINE      CODE     ---       #314
      01000945H   LINE      CODE     ---       #315
      01000947H   LINE      CODE     ---       #316
      01000949H   LINE      CODE     ---       #317
      ---         BLOCKEND  ---      ---       LVL=0

      0100094AH   BLOCK     CODE     ---       LVL=0
      0100094AH   LINE      CODE     ---       #321
      0100094AH   LINE      CODE     ---       #322
      0100094AH   LINE      CODE     ---       #323
      0100094DH   LINE      CODE     ---       #325
      01000950H   LINE      CODE     ---       #326
      01000953H   LINE      CODE     ---       #329
LX51 LINKER/LOCATER V4.66.93.0                                                        06/03/2025  13:41:17  PAGE 13


      01000956H   LINE      CODE     ---       #330
      01000959H   LINE      CODE     ---       #333
      0100095CH   LINE      CODE     ---       #334
      0100095FH   LINE      CODE     ---       #335
      01000961H   LINE      CODE     ---       #336
      01000963H   LINE      CODE     ---       #337
      ---         BLOCKEND  ---      ---       LVL=0

      010008EDH   BLOCK     CODE     ---       LVL=0
      010008EDH   LINE      CODE     ---       #341
      010008EDH   LINE      CODE     ---       #342
      010008EDH   LINE      CODE     ---       #343
      010008F0H   LINE      CODE     ---       #344
      010008F0H   LINE      CODE     ---       #345
      010008F2H   LINE      CODE     ---       #346
      010008F9H   LINE      CODE     ---       #347
      010008FCH   LINE      CODE     ---       #348
      01000904H   LINE      CODE     ---       #349
      01000907H   LINE      CODE     ---       #350
      0100090FH   LINE      CODE     ---       #351
      0100090FH   LINE      CODE     ---       #352
      ---         BLOCKEND  ---      ---       LVL=0

      0100024CH   BLOCK     CODE     ---       LVL=0
      0100024CH   LINE      CODE     ---       #356
      0100024CH   LINE      CODE     ---       #357
      0100024CH   LINE      CODE     ---       #358
      01000252H   LINE      CODE     ---       #359
      01000252H   LINE      CODE     ---       #360
      01000254H   LINE      CODE     ---       #362
      0100025AH   LINE      CODE     ---       #363
      01000267H   LINE      CODE     ---       #364
      01000267H   LINE      CODE     ---       #365
      01000269H   LINE      CODE     ---       #373
      0100026FH   LINE      CODE     ---       #374
      0100026FH   LINE      CODE     ---       #375
      0100027DH   LINE      CODE     ---       #376
      0100028CH   LINE      CODE     ---       #377
      0100028CH   LINE      CODE     ---       #378
      01000290H   LINE      CODE     ---       #379
      01000292H   LINE      CODE     ---       #380
      01000293H   LINE      CODE     ---       #382
      01000293H   LINE      CODE     ---       #383
      010002A8H   LINE      CODE     ---       #384
      010002BCH   LINE      CODE     ---       #385
      010002BCH   LINE      CODE     ---       #386
      010002BCH   LINE      CODE     ---       #387
      010002BFH   LINE      CODE     ---       #388
      010002D3H   LINE      CODE     ---       #389
      010002D3H   LINE      CODE     ---       #390
      010002D3H   LINE      CODE     ---       #391
      010002D6H   LINE      CODE     ---       #392
      010002EAH   LINE      CODE     ---       #393
      010002EAH   LINE      CODE     ---       #394
      010002EDH   LINE      CODE     ---       #395
      010002EDH   LINE      CODE     ---       #396
      010002F0H   LINE      CODE     ---       #398
      010002F0H   LINE      CODE     ---       #399
      010002F1H   LINE      CODE     ---       #400
      010002F1H   LINE      CODE     ---       #401
      010002F1H   LINE      CODE     ---       #402
      010002F4H   LINE      CODE     ---       #405
      010002F4H   LINE      CODE     ---       #406
      010002FAH   LINE      CODE     ---       #407
      010002FAH   LINE      CODE     ---       #408
LX51 LINKER/LOCATER V4.66.93.0                                                        06/03/2025  13:41:17  PAGE 14


      010002FAH   LINE      CODE     ---       #409
      010002FAH   LINE      CODE     ---       #410
      010002FCH   LINE      CODE     ---       #411
      01000308H   LINE      CODE     ---       #412
      01000308H   LINE      CODE     ---       #413
      0100030BH   LINE      CODE     ---       #414
      0100030BH   LINE      CODE     ---       #415
      0100030DH   LINE      CODE     ---       #417
      0100030DH   LINE      CODE     ---       #418
      01000313H   LINE      CODE     ---       #419
      01000313H   LINE      CODE     ---       #420
      01000316H   LINE      CODE     ---       #421
      01000316H   LINE      CODE     ---       #422
      01000318H   LINE      CODE     ---       #424
      01000318H   LINE      CODE     ---       #425
      0100032DH   LINE      CODE     ---       #426
      01000342H   LINE      CODE     ---       #427
      01000342H   LINE      CODE     ---       #428
      01000345H   LINE      CODE     ---       #429
      01000346H   LINE      CODE     ---       #430
      0100035CH   LINE      CODE     ---       #431
      0100035CH   LINE      CODE     ---       #432
      0100035FH   LINE      CODE     ---       #433
      01000360H   LINE      CODE     ---       #434
      01000376H   LINE      CODE     ---       #435
      01000376H   LINE      CODE     ---       #436
      01000379H   LINE      CODE     ---       #437
      01000379H   LINE      CODE     ---       #438
      0100037BH   LINE      CODE     ---       #440
      0100037BH   LINE      CODE     ---       #441
      01000384H   LINE      CODE     ---       #442
      01000384H   LINE      CODE     ---       #443
      01000384H   LINE      CODE     ---       #444
      01000384H   LINE      CODE     ---       #445
      01000384H   LINE      CODE     ---       #446
      01000384H   LINE      CODE     ---       #447
      01000384H   LINE      CODE     ---       #448
      ---         BLOCKEND  ---      ---       LVL=0

      0100052FH   BLOCK     CODE     ---       LVL=0
      0100052FH   BLOCK     CODE     NEAR LAB  LVL=1
      0200005CH   SYMBOL    XDATA    BYTE      temp
      ---         BLOCKEND  ---      ---       LVL=1
      0100052FH   LINE      CODE     ---       #544
      0100052FH   LINE      CODE     ---       #545
      0100052FH   LINE      CODE     ---       #546
      01000534H   LINE      CODE     ---       #548
      01000537H   LINE      CODE     ---       #549
      01000537H   LINE      CODE     ---       #550
      01000539H   LINE      CODE     ---       #552
      0100053CH   LINE      CODE     ---       #553
      0100053FH   LINE      CODE     ---       #554
      01000542H   LINE      CODE     ---       #555
      01000543H   LINE      CODE     ---       #556
      01000544H   LINE      CODE     ---       #557
      01000545H   LINE      CODE     ---       #560
      0100054BH   LINE      CODE     ---       #561
      0100054BH   LINE      CODE     ---       #562
      0100054CH   LINE      CODE     ---       #563
      0100054EH   LINE      CODE     ---       #565
      0100054EH   LINE      CODE     ---       #566
      01000551H   LINE      CODE     ---       #567
      01000551H   LINE      CODE     ---       #569
      0100055EH   LINE      CODE     ---       #570
      0100055EH   LINE      CODE     ---       #571
LX51 LINKER/LOCATER V4.66.93.0                                                        06/03/2025  13:41:17  PAGE 15


      01000560H   LINE      CODE     ---       #572
      01000561H   LINE      CODE     ---       #574
      01000561H   LINE      CODE     ---       #575
      01000568H   LINE      CODE     ---       #576
      01000568H   LINE      CODE     ---       #577
      0100056EH   LINE      CODE     ---       #578
      0100056EH   LINE      CODE     ---       #579
      01000573H   LINE      CODE     ---       #580
      01000573H   LINE      CODE     ---       #581
      01000575H   LINE      CODE     ---       #583
      01000575H   LINE      CODE     ---       #584
      0100057BH   LINE      CODE     ---       #585
      0100057FH   LINE      CODE     ---       #586
      0100057FH   LINE      CODE     ---       #587
      01000580H   LINE      CODE     ---       #589
      01000580H   LINE      CODE     ---       #590
      01000587H   LINE      CODE     ---       #591
      01000587H   LINE      CODE     ---       #592
      0100058CH   LINE      CODE     ---       #593
      01000590H   LINE      CODE     ---       #594
      01000592H   LINE      CODE     ---       #596
      01000592H   LINE      CODE     ---       #597
      01000598H   LINE      CODE     ---       #598
      0100059CH   LINE      CODE     ---       #600
      010005A2H   LINE      CODE     ---       #601
      010005A2H   LINE      CODE     ---       #602
      010005A4H   LINE      CODE     ---       #603
      010005A5H   LINE      CODE     ---       #604
      010005A7H   LINE      CODE     ---       #606
      010005A7H   LINE      CODE     ---       #607
      010005A9H   LINE      CODE     ---       #608
      010005A9H   LINE      CODE     ---       #609
      010005A9H   LINE      CODE     ---       #610
      010005AEH   LINE      CODE     ---       #611
      010005AEH   LINE      CODE     ---       #612
      010005AEH   LINE      CODE     ---       #613
      010005AEH   LINE      CODE     ---       #614
      ---         BLOCKEND  ---      ---       LVL=0

      010008C5H   BLOCK     CODE     ---       LVL=0
      00000008H   SYMBOL    DATA     ---       buff
      00000005H   SYMBOL    DATA     BYTE      buffHeadAdd
      0000000CH   SYMBOL    DATA     BYTE      Len
      010008CBH   BLOCK     CODE     NEAR LAB  LVL=1
      00000007H   SYMBOL    DATA     BYTE      temp
      00000006H   SYMBOL    DATA     BYTE      i
      ---         BLOCKEND  ---      ---       LVL=1
      010008C5H   LINE      CODE     ---       #618
      010008CBH   LINE      CODE     ---       #619
      010008CBH   LINE      CODE     ---       #622
      010008CDH   LINE      CODE     ---       #623
      010008D9H   LINE      CODE     ---       #624
      010008D9H   LINE      CODE     ---       #625
      010008E9H   LINE      CODE     ---       #626
      010008ECH   LINE      CODE     ---       #627
      010008ECH   LINE      CODE     ---       #628
      ---         BLOCKEND  ---      ---       LVL=0

      01000864H   BLOCK     CODE     ---       LVL=0
      01000864H   LINE      CODE     ---       #671
      01000864H   LINE      CODE     ---       #672
      01000864H   LINE      CODE     ---       #674
      01000867H   LINE      CODE     ---       #675
      01000867H   LINE      CODE     ---       #676
      01000869H   LINE      CODE     ---       #678
LX51 LINKER/LOCATER V4.66.93.0                                                        06/03/2025  13:41:17  PAGE 16


      0100086CH   LINE      CODE     ---       #680
      01000872H   LINE      CODE     ---       #681
      01000874H   LINE      CODE     ---       #682
      01000876H   LINE      CODE     ---       #683
      01000878H   LINE      CODE     ---       #684
      0100087AH   LINE      CODE     ---       #685
      0100087CH   LINE      CODE     ---       #686
      0100087EH   LINE      CODE     ---       #687
      01000880H   LINE      CODE     ---       #688
      01000896H   LINE      CODE     ---       #691
      01000898H   LINE      CODE     ---       #692
      01000898H   LINE      CODE     ---       #693
      ---         BLOCKEND  ---      ---       LVL=0

      01000623H   BLOCK     CODE     ---       LVL=0
      01000623H   BLOCK     CODE     NEAR LAB  LVL=1
      00000007H   SYMBOL    DATA     BYTE      i
      ---         BLOCKEND  ---      ---       LVL=1
      01000623H   LINE      CODE     ---       #703
      01000623H   LINE      CODE     ---       #704
      01000623H   LINE      CODE     ---       #707
      01000626H   LINE      CODE     ---       #708
      01000626H   LINE      CODE     ---       #709
      01000629H   LINE      CODE     ---       #710
      01000629H   LINE      CODE     ---       #711
      0100062BH   LINE      CODE     ---       #712
      01000636H   LINE      CODE     ---       #713
      01000636H   LINE      CODE     ---       #714
      01000639H   LINE      CODE     ---       #715
      0100063BH   LINE      CODE     ---       #716
      0100063DH   LINE      CODE     ---       #718
      01000645H   LINE      CODE     ---       #719
      01000651H   LINE      CODE     ---       #720
      01000651H   LINE      CODE     ---       #721
      01000669H   LINE      CODE     ---       #722
      0100066CH   LINE      CODE     ---       #724
      01000678H   LINE      CODE     ---       #725
      01000678H   LINE      CODE     ---       #726
      01000684H   LINE      CODE     ---       #727
      01000687H   LINE      CODE     ---       #729
      01000689H   LINE      CODE     ---       #730
      0100068EH   LINE      CODE     ---       #731
      01000690H   LINE      CODE     ---       #732
      01000692H   LINE      CODE     ---       #733
      01000692H   LINE      CODE     ---       #734
      01000692H   LINE      CODE     ---       #735
      01000692H   LINE      CODE     ---       #736
      ---         BLOCKEND  ---      ---       LVL=0

      01000899H   BLOCK     CODE     ---       LVL=0
      01000899H   LINE      CODE     ---       #742
      01000899H   LINE      CODE     ---       #743
      01000899H   LINE      CODE     ---       #744
      0100089BH   LINE      CODE     ---       #745
      0100089EH   LINE      CODE     ---       #746
      010008A1H   LINE      CODE     ---       #747
      010008A4H   LINE      CODE     ---       #748
      010008A7H   LINE      CODE     ---       #749
      010008AEH   LINE      CODE     ---       #750
      010008B3H   LINE      CODE     ---       #751
      010008B6H   LINE      CODE     ---       #756
      010008B8H   LINE      CODE     ---       #759
      010008BAH   LINE      CODE     ---       #761
      010008BAH   LINE      CODE     ---       #762
      010008BAH   LINE      CODE     ---       #763
LX51 LINKER/LOCATER V4.66.93.0                                                        06/03/2025  13:41:17  PAGE 17


      010008BDH   LINE      CODE     ---       #767
      010008C0H   LINE      CODE     ---       #768
      010008C3H   LINE      CODE     ---       #769
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       INTERRUPT
      00000015H   PUBLIC    DATA     BYTE      loop_20ms_exit
      00000020H.5 PUBLIC    BIT      BIT       gbFg10msCH1
      02000047H   PUBLIC    XDATA    BYTE      gbUart0TxdLen
      02000026H   PUBLIC    XDATA    ---       gbUart0Buff1
      00000020H.4 PUBLIC    BIT      BIT       gbFg_Uart0SendRec
      00000020H.3 PUBLIC    BIT      BIT       gbFg5msCH1
      00000014H   PUBLIC    DATA     BYTE      SBUFTemp
      02000025H   PUBLIC    XDATA    BYTE      gbUart0Len
      00000020H.2 PUBLIC    BIT      BIT       gbFg_Uart0Ack
      02000004H   PUBLIC    XDATA    ---       gbUart0Buff
      02000003H   PUBLIC    XDATA    BYTE      gbUart0Count500ms
      00000020H.1 PUBLIC    BIT      BIT       gbFg_Uart1ReOk
      02000002H   PUBLIC    XDATA    BYTE      gbUart0Head
      00000020H.0 PUBLIC    BIT      BIT       gbFg_Uart0RxEnd
      02000001H   PUBLIC    XDATA    BYTE      gb500msCnt485
      02000000H   PUBLIC    XDATA    BYTE      gbUart0RxLen
      0100005EH   PUBLIC    CODE     ---       ELPD_INT
      01000057H   PUBLIC    CODE     ---       PWM_INT
      0100002EH   PUBLIC    CODE     ---       SCM_INT
      0100004EH   PUBLIC    CODE     ---       EX2_INT
      01000026H   PUBLIC    CODE     ---       ADC_INT
      01000016H   PUBLIC    CODE     ---       Timer2_INT
      01000076H   PUBLIC    CODE     ---       EUART0_INT
      0100000EH   PUBLIC    CODE     ---       Timer1_INT
      01000006H   PUBLIC    CODE     ---       EX1_INT
      01000693H   PUBLIC    CODE     ---       Timer0_INT
      0100002AH   PUBLIC    CODE     ---       EX0_INT
      0000000FH   PUBLIC    DATA     BYTE      ?_CalCheckSum1?BYTE
      01000800H   PUBLIC    CODE     ---       _CalCheckSum1
      000000EAH   SFRSYM    DATA     BYTE      P1M0
      000000F3H   SFRSYM    DATA     BYTE      IB_CON2
      000000E8H.2 SFRSYM    DATA     BIT       IT20
      000000E2H   SFRSYM    DATA     BYTE      P1M1
      000000F4H   SFRSYM    DATA     BYTE      IB_CON3
      000000E8H.3 SFRSYM    DATA     BIT       IT21
      000000ECH   SFRSYM    DATA     BYTE      P3M0
      00000090H   SFRSYM    DATA     BYTE      P1
      000000F5H   SFRSYM    DATA     BYTE      IB_CON4
      000000EDH   SFRSYM    DATA     BYTE      P4M0
      000000E4H   SFRSYM    DATA     BYTE      P3M1
      000000F6H   SFRSYM    DATA     BYTE      IB_CON5
      00000098H.6 SFRSYM    DATA     BIT       SM1_RXOV
      000000E5H   SFRSYM    DATA     BYTE      P4M1
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000FCH   SFRSYM    DATA     BYTE      IB_DATA
      000000A8H   SFRSYM    DATA     BYTE      IEN0
      000000A8H.6 SFRSYM    DATA     BIT       EADC
      000000A9H   SFRSYM    DATA     BYTE      IEN1
      00000085H   SFRSYM    DATA     BYTE      DPH1
      00000095H   SFRSYM    DATA     BYTE      ADCH
      00000097H   SFRSYM    DATA     BYTE      ADDH
      000000B4H   SFRSYM    DATA     BYTE      IPH0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000B0H.0 SFRSYM    DATA     BIT       P3_0
      00000090H.2 SFRSYM    DATA     BIT       P1_2
      000000B5H   SFRSYM    DATA     BYTE      IPH1
LX51 LINKER/LOCATER V4.66.93.0                                                        06/03/2025  13:41:17  PAGE 18


      000000C0H.0 SFRSYM    DATA     BIT       P4_0
      000000B0H.1 SFRSYM    DATA     BIT       P3_1
      00000090H.3 SFRSYM    DATA     BIT       P1_3
      000000E8H   SFRSYM    DATA     BYTE      EXF0
      000000C0H.1 SFRSYM    DATA     BIT       P4_1
      000000B0H.2 SFRSYM    DATA     BIT       P3_2
      00000090H.4 SFRSYM    DATA     BIT       P1_4
      000000C0H.2 SFRSYM    DATA     BIT       P4_2
      000000B0H.3 SFRSYM    DATA     BIT       P3_3
      00000090H.5 SFRSYM    DATA     BIT       P1_5
      000000C8H.6 SFRSYM    DATA     BIT       EXF2
      00000096H   SFRSYM    DATA     BYTE      ADDL
      000000B8H   SFRSYM    DATA     BYTE      IPL0
      00000090H.6 SFRSYM    DATA     BIT       P1_6
      000000B9H   SFRSYM    DATA     BYTE      IPL1
      00000090H.7 SFRSYM    DATA     BIT       P1_7
      0000008EH   SFRSYM    DATA     BYTE      SUSLO
      000000B0H.7 SFRSYM    DATA     BIT       P3_7
      00000098H.0 SFRSYM    DATA     BIT       RI
      000000D0H.7 SFRSYM    DATA     BIT       CY
      00000098H.1 SFRSYM    DATA     BIT       TI
      000000B8H.1 SFRSYM    DATA     BIT       PT0L
      000000B8H.3 SFRSYM    DATA     BIT       PT1L
      000000CBH   SFRSYM    DATA     BYTE      RCAP2H
      000000B8H.5 SFRSYM    DATA     BIT       PT2L
      00000081H   SFRSYM    DATA     BYTE      SP
      000000B8H.0 SFRSYM    DATA     BIT       PX0L
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000B8H.2 SFRSYM    DATA     BIT       PX1L
      000000CAH   SFRSYM    DATA     BYTE      RCAP2L
      000000C8H.1 SFRSYM    DATA     BIT       C_T2
      000000C8H.5 SFRSYM    DATA     BIT       RCLK
      000000C8H.4 SFRSYM    DATA     BIT       TCLK
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000F1H   SFRSYM    DATA     BYTE      AUXC
      00000098H   SFRSYM    DATA     BYTE      SCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000FBH   SFRSYM    DATA     BYTE      IB_OFFSET
      000000B1H   SFRSYM    DATA     BYTE      RSTSTAT
      000000D3H   SFRSYM    DATA     BYTE      PWMD
      00000098H.7 SFRSYM    DATA     BIT       SM0_FE
      000000B2H   SFRSYM    DATA     BYTE      CLKCON
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      00000098H.5 SFRSYM    DATA     BIT       SM2_TXCOL
      000000E8H.0 SFRSYM    DATA     BIT       IE2
      000000F0H   SFRSYM    DATA     BYTE      B
      000000B3H   SFRSYM    DATA     BYTE      LPDCON
      000000C8H.0 SFRSYM    DATA     BIT       CP_RL2
      000000D2H   SFRSYM    DATA     BYTE      PWMP
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.7 SFRSYM    DATA     BIT       TF1
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.2 SFRSYM    DATA     BIT       RB8
      000000C8H.7 SFRSYM    DATA     BIT       TF2
      0000008CH   SFRSYM    DATA     BYTE      TH0
      00000086H   SFRSYM    DATA     BYTE      INSCON
      00000088H.0 SFRSYM    DATA     BIT       IT0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
LX51 LINKER/LOCATER V4.66.93.0                                                        06/03/2025  13:41:17  PAGE 19


      0000008DH   SFRSYM    DATA     BYTE      TH1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      00000098H.3 SFRSYM    DATA     BIT       TB8
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      000000A7H   SFRSYM    DATA     BYTE      FLASHCON
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000D1H   SFRSYM    DATA     BYTE      PWMCON
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000C8H.2 SFRSYM    DATA     BIT       TR2
      00000094H   SFRSYM    DATA     BYTE      ADT
      00000083H   SFRSYM    DATA     BYTE      DPH
      00000082H   SFRSYM    DATA     BYTE      DPL
      000000C8H.3 SFRSYM    DATA     BIT       EXEN2
      00000098H.4 SFRSYM    DATA     BIT       REN
      000000B8H.6 SFRSYM    DATA     BIT       PADCL
      00000093H   SFRSYM    DATA     BYTE      ADCON
      000000CEH   SFRSYM    DATA     BYTE      TCON1
      000000C9H   SFRSYM    DATA     BYTE      T2MOD
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      0000009BH   SFRSYM    DATA     BYTE      SADEN
      000000B8H.4 SFRSYM    DATA     BIT       PSL
      0000009AH   SFRSYM    DATA     BYTE      SADDR
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000D0H.1 SFRSYM    DATA     BIT       F1
      000000F7H   SFRSYM    DATA     BYTE      XPAGE
      000000D0H   SFRSYM    DATA     BYTE      PSW
      000000F2H   SFRSYM    DATA     BYTE      IB_CON1

      010007EAH   BLOCK     CODE     VOID      LVL=0
      ---         BLOCKEND  ---      ---       LVL=0

      01000800H   BLOCK     CODE     ---       LVL=0
      0000000FH   SYMBOL    DATA     ---       buff
      00000005H   SYMBOL    DATA     BYTE      buffHeadAdd
      00000013H   SYMBOL    DATA     BYTE      Len
      00000007H   SYMBOL    DATA     BYTE      temp
      00000006H   SYMBOL    DATA     BYTE      i
      01000806H   BLOCK     CODE     NEAR LAB  LVL=1
      00000007H   SYMBOL    DATA     BYTE      temp
      00000006H   SYMBOL    DATA     BYTE      i
      ---         BLOCKEND  ---      ---       LVL=1
      01000800H   LINE      CODE     ---       #104
      01000806H   LINE      CODE     ---       #105
      01000806H   LINE      CODE     ---       #108
      01000808H   LINE      CODE     ---       #109
      01000814H   LINE      CODE     ---       #110
      01000814H   LINE      CODE     ---       #111
      01000824H   LINE      CODE     ---       #112
      01000827H   LINE      CODE     ---       #113
      01000827H   LINE      CODE     ---       #114
      ---         BLOCKEND  ---      ---       LVL=0

      0100002AH   BLOCK     CODE     ---       LVL=0
      0100002AH   LINE      CODE     ---       #119
      0100002AH   LINE      CODE     ---       #122
      ---         BLOCKEND  ---      ---       LVL=0

      01000693H   BLOCK     CODE     ---       LVL=0
LX51 LINKER/LOCATER V4.66.93.0                                                        06/03/2025  13:41:17  PAGE 20


      01000693H   LINE      CODE     ---       #126
      010006B0H   LINE      CODE     ---       #128
      010006B2H   LINE      CODE     ---       #129
      010006B4H   LINE      CODE     ---       #130
      010006B7H   LINE      CODE     ---       #131
      010006BAH   LINE      CODE     ---       #132
      010006BCH   LINE      CODE     ---       #134
      010006BEH   LINE      CODE     ---       #135
      010006C0H   LINE      CODE     ---       #137
      010006C2H   LINE      CODE     ---       #139
      010006D0H   LINE      CODE     ---       #140
      010006D0H   LINE      CODE     ---       #142
      010006D0H   LINE      CODE     ---       #143
      010006D5H   LINE      CODE     ---       #144
      010006D5H   LINE      CODE     ---       #146
      010006D8H   LINE      CODE     ---       #148
      010006DAH   LINE      CODE     ---       #149
      010006DCH   LINE      CODE     ---       #151
      010006DCH   LINE      CODE     ---       #152
      010006DEH   LINE      CODE     ---       #153
      010006DEH   LINE      CODE     ---       #155
      010006E1H   LINE      CODE     ---       #156
      ---         BLOCKEND  ---      ---       LVL=0

      01000006H   BLOCK     CODE     ---       LVL=0
      01000006H   LINE      CODE     ---       #160
      01000006H   LINE      CODE     ---       #162
      01000008H   LINE      CODE     ---       #163
      0100000AH   LINE      CODE     ---       #165
      ---         BLOCKEND  ---      ---       LVL=0

      0100000EH   BLOCK     CODE     ---       LVL=0
      0100000EH   LINE      CODE     ---       #170
      0100000EH   LINE      CODE     ---       #172
      01000010H   LINE      CODE     ---       #173
      01000012H   LINE      CODE     ---       #175
      ---         BLOCKEND  ---      ---       LVL=0

      01000076H   BLOCK     CODE     ---       LVL=0
      01000091H   BLOCK     CODE     NEAR LAB  LVL=1
      0000000DH   SYMBOL    DATA     BYTE      i
      0000000EH   SYMBOL    DATA     BYTE      checksum
      ---         BLOCKEND  ---      ---       LVL=1
      01000076H   LINE      CODE     ---       #181
      01000091H   LINE      CODE     ---       #183
      01000094H   LINE      CODE     ---       #185
      01000097H   LINE      CODE     ---       #187
      0100009AH   LINE      CODE     ---       #188
      0100009AH   LINE      CODE     ---       #189
      0100009CH   LINE      CODE     ---       #191
      0100009FH   LINE      CODE     ---       #192
      0100009FH   LINE      CODE     ---       #193
      010000ADH   LINE      CODE     ---       #194
      010000ADH   LINE      CODE     ---       #195
      010000B8H   LINE      CODE     ---       #196
      010000BCH   LINE      CODE     ---       #197
      010000BEH   LINE      CODE     ---       #198
      010000C4H   LINE      CODE     ---       #199
      010000C6H   LINE      CODE     ---       #201
      010000C6H   LINE      CODE     ---       #202
      010000D4H   LINE      CODE     ---       #203
      010000D4H   LINE      CODE     ---       #204
      010000E1H   LINE      CODE     ---       #205
      010000E5H   LINE      CODE     ---       #206
      010000EAH   LINE      CODE     ---       #207
LX51 LINKER/LOCATER V4.66.93.0                                                        06/03/2025  13:41:17  PAGE 21


      010000EEH   LINE      CODE     ---       #208
      010000F0H   LINE      CODE     ---       #209
      010000F2H   LINE      CODE     ---       #212
      010000F5H   LINE      CODE     ---       #214
      010000F5H   LINE      CODE     ---       #215
      010000F5H   LINE      CODE     ---       #216
      010000F5H   LINE      CODE     ---       #218
      010000FBH   LINE      CODE     ---       #219
      010000FBH   LINE      CODE     ---       #220
      010000FDH   LINE      CODE     ---       #222
      01000103H   LINE      CODE     ---       #223
      01000103H   LINE      CODE     ---       #224
      01000105H   LINE      CODE     ---       #225
      01000110H   LINE      CODE     ---       #226
      01000110H   LINE      CODE     ---       #227
      01000115H   LINE      CODE     ---       #229
      01000117H   LINE      CODE     ---       #231
      0100011AH   LINE      CODE     ---       #235
      01000120H   LINE      CODE     ---       #236
      0100012EH   LINE      CODE     ---       #237
      0100012EH   LINE      CODE     ---       #238
      01000148H   LINE      CODE     ---       #239
      0100014CH   LINE      CODE     ---       #242
      0100015DH   LINE      CODE     ---       #243
      0100015DH   LINE      CODE     ---       #244
      01000163H   LINE      CODE     ---       #245
      01000189H   LINE      CODE     ---       #246
      01000189H   LINE      CODE     ---       #247
      010001A9H   LINE      CODE     ---       #248
      010001A9H   LINE      CODE     ---       #249
      010001C7H   LINE      CODE     ---       #250
      010001C7H   LINE      CODE     ---       #251
      010001C7H   LINE      CODE     ---       #252
      010001C7H   LINE      CODE     ---       #253
      010001C7H   LINE      CODE     ---       #254
      010001C7H   LINE      CODE     ---       #255
      010001C7H   LINE      CODE     ---       #256
      010001C9H   LINE      CODE     ---       #257
      010001C9H   LINE      CODE     ---       #259
      010001C9H   LINE      CODE     ---       #260
      010001C9H   LINE      CODE     ---       #261
      010001C9H   LINE      CODE     ---       #262
      010001C9H   LINE      CODE     ---       #263
      010001C9H   LINE      CODE     ---       #264
      010001C9H   LINE      CODE     ---       #265
      010001C9H   LINE      CODE     ---       #266
      010001CBH   LINE      CODE     ---       #268
      010001CBH   LINE      CODE     ---       #269
      010001DBH   LINE      CODE     ---       #270
      010001DBH   LINE      CODE     ---       #271
      010001F9H   LINE      CODE     ---       #272
      010001F9H   LINE      CODE     ---       #273
      010001FBH   LINE      CODE     ---       #274
      010001FDH   LINE      CODE     ---       #275
      010001FFH   LINE      CODE     ---       #276
      01000204H   LINE      CODE     ---       #277
      01000206H   LINE      CODE     ---       #278
      01000208H   LINE      CODE     ---       #279
      01000208H   LINE      CODE     ---       #281
      01000208H   LINE      CODE     ---       #282
      01000208H   LINE      CODE     ---       #283
      01000208H   LINE      CODE     ---       #284
      01000208H   LINE      CODE     ---       #285
      01000208H   LINE      CODE     ---       #286
      01000208H   LINE      CODE     ---       #287
LX51 LINKER/LOCATER V4.66.93.0                                                        06/03/2025  13:41:17  PAGE 22


      01000208H   LINE      CODE     ---       #288
      01000208H   LINE      CODE     ---       #289
      01000208H   LINE      CODE     ---       #290
      0100020AH   LINE      CODE     ---       #292
      0100020AH   LINE      CODE     ---       #293
      0100020FH   LINE      CODE     ---       #294
      01000213H   LINE      CODE     ---       #295
      01000215H   LINE      CODE     ---       #296
      01000217H   LINE      CODE     ---       #297
      0100021BH   LINE      CODE     ---       #298
      0100021BH   LINE      CODE     ---       #299
      01000220H   LINE      CODE     ---       #327
      01000220H   LINE      CODE     ---       #329
      01000220H   LINE      CODE     ---       #330
      01000225H   LINE      CODE     ---       #331
      01000229H   LINE      CODE     ---       #332
      0100022BH   LINE      CODE     ---       #333
      0100022DH   LINE      CODE     ---       #334
      01000231H   LINE      CODE     ---       #335
      01000233H   LINE      CODE     ---       #336
      01000233H   LINE      CODE     ---       #337
      01000233H   LINE      CODE     ---       #338
      01000233H   LINE      CODE     ---       #339
      ---         BLOCKEND  ---      ---       LVL=0

      01000016H   BLOCK     CODE     ---       LVL=0
      01000016H   LINE      CODE     ---       #343
      01000016H   LINE      CODE     ---       #345
      01000018H   LINE      CODE     ---       #346
      0100001AH   LINE      CODE     ---       #348
      ---         BLOCKEND  ---      ---       LVL=0

      01000026H   BLOCK     CODE     ---       LVL=0
      01000026H   LINE      CODE     ---       #352
      01000026H   LINE      CODE     ---       #354
      01000029H   LINE      CODE     ---       #355
      ---         BLOCKEND  ---      ---       LVL=0

      0100004EH   BLOCK     CODE     ---       LVL=0
      0100004EH   LINE      CODE     ---       #359
      0100004EH   LINE      CODE     ---       #361
      01000051H   LINE      CODE     ---       #362
      01000054H   LINE      CODE     ---       #363
      01000056H   LINE      CODE     ---       #365
      ---         BLOCKEND  ---      ---       LVL=0

      0100002EH   BLOCK     CODE     ---       LVL=0
      0100002EH   LINE      CODE     ---       #372
      0100002EH   LINE      CODE     ---       #374
      01000031H   LINE      CODE     ---       #376
      ---         BLOCKEND  ---      ---       LVL=0

      01000057H   BLOCK     CODE     ---       LVL=0
      01000057H   LINE      CODE     ---       #380
      01000057H   LINE      CODE     ---       #382
      0100005AH   LINE      CODE     ---       #384
      ---         BLOCKEND  ---      ---       LVL=0

      0100005EH   BLOCK     CODE     ---       LVL=0
      0100005EH   LINE      CODE     ---       #388
      0100005EH   LINE      CODE     ---       #390
      01000061H   LINE      CODE     ---       #392
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       UARTDRIVER
LX51 LINKER/LOCATER V4.66.93.0                                                        06/03/2025  13:41:17  PAGE 23


      01000964H   PUBLIC    CODE     ---       _UartConf
      0100097CH   PUBLIC    CODE     ---       UartIntEnable
      0100006DH   PUBLIC    CODE     ---       UartIntDisable
      01000066H   PUBLIC    CODE     ---       UARTSendOn
      0100001EH   PUBLIC    CODE     ---       UARTReceiveOn
      01000828H   PUBLIC    CODE     ---       _UartBaudSet
      01000036H   PUBLIC    CODE     ---       UartInit
      000000EAH   SFRSYM    DATA     BYTE      P1M0
      000000F3H   SFRSYM    DATA     BYTE      IB_CON2
      000000E8H.2 SFRSYM    DATA     BIT       IT20
      000000E2H   SFRSYM    DATA     BYTE      P1M1
      000000F4H   SFRSYM    DATA     BYTE      IB_CON3
      000000E8H.3 SFRSYM    DATA     BIT       IT21
      000000ECH   SFRSYM    DATA     BYTE      P3M0
      00000090H   SFRSYM    DATA     BYTE      P1
      000000F5H   SFRSYM    DATA     BYTE      IB_CON4
      000000EDH   SFRSYM    DATA     BYTE      P4M0
      000000E4H   SFRSYM    DATA     BYTE      P3M1
      000000F6H   SFRSYM    DATA     BYTE      IB_CON5
      00000098H.6 SFRSYM    DATA     BIT       SM1_RXOV
      000000E5H   SFRSYM    DATA     BYTE      P4M1
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000FCH   SFRSYM    DATA     BYTE      IB_DATA
      000000A8H   SFRSYM    DATA     BYTE      IEN0
      000000A8H.6 SFRSYM    DATA     BIT       EADC
      000000A9H   SFRSYM    DATA     BYTE      IEN1
      00000085H   SFRSYM    DATA     BYTE      DPH1
      00000095H   SFRSYM    DATA     BYTE      ADCH
      00000097H   SFRSYM    DATA     BYTE      ADDH
      000000B4H   SFRSYM    DATA     BYTE      IPH0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000B0H.0 SFRSYM    DATA     BIT       P3_0
      00000090H.2 SFRSYM    DATA     BIT       P1_2
      000000B5H   SFRSYM    DATA     BYTE      IPH1
      000000C0H.0 SFRSYM    DATA     BIT       P4_0
      000000B0H.1 SFRSYM    DATA     BIT       P3_1
      00000090H.3 SFRSYM    DATA     BIT       P1_3
      000000E8H   SFRSYM    DATA     BYTE      EXF0
      000000C0H.1 SFRSYM    DATA     BIT       P4_1
      000000B0H.2 SFRSYM    DATA     BIT       P3_2
      00000090H.4 SFRSYM    DATA     BIT       P1_4
      000000C0H.2 SFRSYM    DATA     BIT       P4_2
      000000B0H.3 SFRSYM    DATA     BIT       P3_3
      00000090H.5 SFRSYM    DATA     BIT       P1_5
      000000C8H.6 SFRSYM    DATA     BIT       EXF2
      00000096H   SFRSYM    DATA     BYTE      ADDL
      000000B8H   SFRSYM    DATA     BYTE      IPL0
      00000090H.6 SFRSYM    DATA     BIT       P1_6
      000000B9H   SFRSYM    DATA     BYTE      IPL1
      00000090H.7 SFRSYM    DATA     BIT       P1_7
      0000008EH   SFRSYM    DATA     BYTE      SUSLO
      000000B0H.7 SFRSYM    DATA     BIT       P3_7
      00000098H.0 SFRSYM    DATA     BIT       RI
      000000D0H.7 SFRSYM    DATA     BIT       CY
      00000098H.1 SFRSYM    DATA     BIT       TI
      000000B8H.1 SFRSYM    DATA     BIT       PT0L
      000000B8H.3 SFRSYM    DATA     BIT       PT1L
      000000CBH   SFRSYM    DATA     BYTE      RCAP2H
      000000B8H.5 SFRSYM    DATA     BIT       PT2L
      00000081H   SFRSYM    DATA     BYTE      SP
      000000B8H.0 SFRSYM    DATA     BIT       PX0L
      000000D0H.2 SFRSYM    DATA     BIT       OV
LX51 LINKER/LOCATER V4.66.93.0                                                        06/03/2025  13:41:17  PAGE 24


      000000B8H.2 SFRSYM    DATA     BIT       PX1L
      000000CAH   SFRSYM    DATA     BYTE      RCAP2L
      000000C8H.1 SFRSYM    DATA     BIT       C_T2
      000000C8H.5 SFRSYM    DATA     BIT       RCLK
      000000C8H.4 SFRSYM    DATA     BIT       TCLK
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000F1H   SFRSYM    DATA     BYTE      AUXC
      00000098H   SFRSYM    DATA     BYTE      SCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000FBH   SFRSYM    DATA     BYTE      IB_OFFSET
      000000B1H   SFRSYM    DATA     BYTE      RSTSTAT
      000000D3H   SFRSYM    DATA     BYTE      PWMD
      00000098H.7 SFRSYM    DATA     BIT       SM0_FE
      000000B2H   SFRSYM    DATA     BYTE      CLKCON
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      00000098H.5 SFRSYM    DATA     BIT       SM2_TXCOL
      000000E8H.0 SFRSYM    DATA     BIT       IE2
      000000F0H   SFRSYM    DATA     BYTE      B
      000000B3H   SFRSYM    DATA     BYTE      LPDCON
      000000C8H.0 SFRSYM    DATA     BIT       CP_RL2
      000000D2H   SFRSYM    DATA     BYTE      PWMP
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.7 SFRSYM    DATA     BIT       TF1
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.2 SFRSYM    DATA     BIT       RB8
      000000C8H.7 SFRSYM    DATA     BIT       TF2
      0000008CH   SFRSYM    DATA     BYTE      TH0
      00000086H   SFRSYM    DATA     BYTE      INSCON
      00000088H.0 SFRSYM    DATA     BIT       IT0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      00000098H.3 SFRSYM    DATA     BIT       TB8
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      000000A7H   SFRSYM    DATA     BYTE      FLASHCON
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000D1H   SFRSYM    DATA     BYTE      PWMCON
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000C8H.2 SFRSYM    DATA     BIT       TR2
      00000094H   SFRSYM    DATA     BYTE      ADT
      00000083H   SFRSYM    DATA     BYTE      DPH
      00000082H   SFRSYM    DATA     BYTE      DPL
      000000C8H.3 SFRSYM    DATA     BIT       EXEN2
      00000098H.4 SFRSYM    DATA     BIT       REN
      000000B8H.6 SFRSYM    DATA     BIT       PADCL
      00000093H   SFRSYM    DATA     BYTE      ADCON
      000000CEH   SFRSYM    DATA     BYTE      TCON1
      000000C9H   SFRSYM    DATA     BYTE      T2MOD
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      0000009BH   SFRSYM    DATA     BYTE      SADEN
      000000B8H.4 SFRSYM    DATA     BIT       PSL
LX51 LINKER/LOCATER V4.66.93.0                                                        06/03/2025  13:41:17  PAGE 25


      0000009AH   SFRSYM    DATA     BYTE      SADDR
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000D0H.1 SFRSYM    DATA     BIT       F1
      000000F7H   SFRSYM    DATA     BYTE      XPAGE
      000000D0H   SFRSYM    DATA     BYTE      PSW
      000000F2H   SFRSYM    DATA     BYTE      IB_CON1

      01000036H   BLOCK     CODE     ---       LVL=0
      01000036H   LINE      CODE     ---       #18
      01000036H   LINE      CODE     ---       #19
      01000036H   LINE      CODE     ---       #20
      01000039H   LINE      CODE     ---       #21
      0100003CH   LINE      CODE     ---       #23
      0100003FH   LINE      CODE     ---       #24
      01000042H   LINE      CODE     ---       #25
      01000045H   LINE      CODE     ---       #28
      01000048H   LINE      CODE     ---       #31
      ---         BLOCKEND  ---      ---       LVL=0

      01000828H   BLOCK     CODE     ---       LVL=0
      00000006H   SYMBOL    DATA     WORD      baudrate
      01000828H   BLOCK     CODE     NEAR LAB  LVL=1
      0200005AH   SYMBOL    XDATA    WORD      iBaudRate
      ---         BLOCKEND  ---      ---       LVL=1
      01000828H   LINE      CODE     ---       #34
      01000828H   LINE      CODE     ---       #35
      01000828H   LINE      CODE     ---       #38
      01000846H   LINE      CODE     ---       #39
      01000849H   LINE      CODE     ---       #40
      0100084CH   LINE      CODE     ---       #41
      0100084EH   LINE      CODE     ---       #42
      01000850H   LINE      CODE     ---       #43
      01000853H   LINE      CODE     ---       #44
      01000856H   LINE      CODE     ---       #45
      01000859H   LINE      CODE     ---       #46
      0100085BH   LINE      CODE     ---       #48
      0100085EH   LINE      CODE     ---       #50
      01000861H   LINE      CODE     ---       #51
      01000863H   LINE      CODE     ---       #52
      ---         BLOCKEND  ---      ---       LVL=0

      0100001EH   BLOCK     CODE     ---       LVL=0
      0100001EH   LINE      CODE     ---       #55
      0100001EH   LINE      CODE     ---       #56
      0100001EH   LINE      CODE     ---       #57
      01000020H   LINE      CODE     ---       #58
      01000022H   LINE      CODE     ---       #59
      ---         BLOCKEND  ---      ---       LVL=0

      01000066H   BLOCK     CODE     ---       LVL=0
      01000066H   LINE      CODE     ---       #62
      01000066H   LINE      CODE     ---       #63
      01000066H   LINE      CODE     ---       #64
      01000068H   LINE      CODE     ---       #65
      0100006AH   LINE      CODE     ---       #66
      0100006CH   LINE      CODE     ---       #67
      ---         BLOCKEND  ---      ---       LVL=0

      0100006DH   BLOCK     CODE     ---       LVL=0
      0100006DH   LINE      CODE     ---       #70
      0100006DH   LINE      CODE     ---       #71
      0100006DH   LINE      CODE     ---       #72
      01000070H   LINE      CODE     ---       #73
      ---         BLOCKEND  ---      ---       LVL=0

LX51 LINKER/LOCATER V4.66.93.0                                                        06/03/2025  13:41:17  PAGE 26


      0100097CH   BLOCK     CODE     ---       LVL=0
      0100097CH   LINE      CODE     ---       #77
      0100097CH   LINE      CODE     ---       #78
      0100097CH   LINE      CODE     ---       #79
      0100097FH   LINE      CODE     ---       #80
      ---         BLOCKEND  ---      ---       LVL=0

      01000964H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      state
      01000964H   LINE      CODE     ---       #86
      01000964H   LINE      CODE     ---       #87
      01000964H   LINE      CODE     ---       #88
      01000967H   LINE      CODE     ---       #90
      0100096DH   LINE      CODE     ---       #91
      0100096DH   LINE      CODE     ---       #92
      0100096DH   LINE      CODE     ---       #93
      0100096FH   LINE      CODE     ---       #94
      01000975H   LINE      CODE     ---       #95
      01000975H   LINE      CODE     ---       #96
      01000978H   LINE      CODE     ---       #97
      01000978H   LINE      CODE     ---       #99
      01000978H   LINE      CODE     ---       #100
      0100097BH   LINE      CODE     ---       #101
      0100097BH   LINE      CODE     ---       #102
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       ?C_INIT
      010004EAH   PUBLIC    CODE     ---       ?C_START

      ---         MODULE    ---      ---       ?C?CLDOPTR
      01000385H   PUBLIC    CODE     ---       ?C?CLDOPTR

      ---         MODULE    ---      ---       ?C?CSTPTR
      010003B2H   PUBLIC    CODE     ---       ?C?CSTPTR

      ---         MODULE    ---      ---       ?C?SLDIV
      01000763H   PUBLIC    CODE     ---       ?C?SLDIV

      ---         MODULE    ---      ---       ?C?ULCMP
      010003C4H   PUBLIC    CODE     ---       ?C?ULCMP

      ---         MODULE    ---      ---       ?C?ULDIV
      01000411H   PUBLIC    CODE     ---       ?C?ULDIV



*** WARNING L57: UNCALLED FUNCTION, IGNORED FOR OVERLAY PROCESS
    NAME:    INITLEDTESTMODE/MAIN

*** WARNING L57: UNCALLED FUNCTION, IGNORED FOR OVERLAY PROCESS
    NAME:    SCANBUTTON/MAIN

Program Size: data=57.3 xdata=93 const=0 code=2426
LX51 RUN COMPLETE.  2 WARNING(S),  0 ERROR(S)
