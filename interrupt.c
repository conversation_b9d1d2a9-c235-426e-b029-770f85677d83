/********************************************************
中断服务程序:
********************************************************/
#include <constant.H>
#include "define.h"

#define  CAL_BAUDRATE(X)       ((ushort)(32768-(ushort)(614400/(X))))	//波特率计算

bit gbFg_Uart1ReOk;
bit gbFg5msCH1 = 0;
bit gbFg10msCH1 = 0;



//UART串口设置
#define UART_MAX_BUFF    	 212						//10 通讯buf的大小
#define COM_CHANNEL_NUMBLE   3                          //总共的串口数目





typedef struct
{
  	uchar baudRate;
  	uchar bitPEN:2;                                     //奇偶校验，00=无，01=偶校验，11=奇校验
  	uchar bitDataLength:2;                              //数据位的长度，0,2,3=8位，1=DATALENS_7BIT
  	uchar bitStopLength:1;                              //停止位的长度,0=1位，1=2位
  	uchar bitRecerve:3;                                 //预留位
}STR_CONFIG_COM;


typedef  struct
{
  	uchar lens;                          				//帧长度，即缓存区的长度
  	uchar* commExplainBuf;   							//接收缓存
  	uchar  channel;										//串口号
}STR_COMM_INTERFACE;



//------------------------------------------------------------------------------
//------------------------------------------------------------------------------
//------------------------------------------------------------------------------
typedef struct
{
	uchar len;
	uchar ucComBuf[UART_MAX_BUFF];
}STR_COM_BUF;
	



extern xdata STR_CONFIG_COM   commConfig;
extern data STR_COMM_INTERFACE   commInterface;
extern xdata STR_COM_BUF  ucComBuf[COM_CHANNEL_NUMBLE];

extern uchar InitUART(uchar channel);

//配置控制寄存器
extern uchar ConfigUART(uchar ucComChannel,STR_CONFIG_COM* strParameter);

//使能或关闭收发
extern uchar UARTReceiveSwitch(uchar ucComChannel,uchar ucEnableFlag);
extern uchar UARTSendSwitch(uchar ucComChannel,uchar ucEnableFlag);

//发送和接受，一个字节的数据
extern uchar SendByteByUART (uchar ucComChannel,uchar ucSendDataBuf);
extern uchar ReceiveByteByUART(uchar ucComChannel,uchar ucReceiveDataBuf);

extern unsigned char CalCheckSum(unsigned char *buff,unsigned char buffHeadAdd,unsigned char Len);
unsigned char CalCheckSum1(unsigned char *buff,unsigned char buffHeadAdd,unsigned char Len);



extern bit gBFg_Uart0_1ms_ack;

unsigned char xdata gbUart0Head;
unsigned char xdata gb500msCnt485;
unsigned char xdata gbUart0Buff[MAX_UART_DATA_LEN]; 
unsigned char xdata gbUart0Buff1[MAX_UART_DATA_LEN]; 
unsigned char xdata gbUart0Len;
unsigned char xdata gbUart0TxdLen;
unsigned char xdata gbUart0RxLen;
bit gbFg_Uart0SendRec;
bit gbFg_Uart0RxEnd;
bit gbFg_Uart0Ack;



unsigned char data SBUFTemp;
unsigned char xdata gbUart0Count500ms;



extern bit gBFg_ScanButton_1ms;
extern bit gBFg_1ms;
extern void LEDProc(void);

unsigned char data loop_20ms_exit;


/////////////////////////////////////////////////////////////////////////////
unsigned char CalCheckSum1(unsigned char *buff,unsigned char buffHeadAdd,unsigned char Len)
{
	unsigned char data temp,i;
	
	temp = 0;
	for(i=buffHeadAdd;i<(buffHeadAdd+Len-1);i++)
	{
		temp = 	temp + buff[i];
	}
	return(temp);
}


///////////////////////////////////////////////////////////////////////////////
//外部中断0服务程序:
void EX0_INT(void)    interrupt 0	          	
{

}

///////////////////////////////////////////////////////////////////////////////
//定时中断0服务程序:
void Timer0_INT(void) interrupt 1	  		 	//1MS
{  
	TF0 = 0;	   								//TCON.5，由硬件自动清零
  TR0 = 0;
	TL0 = 0xCB;			        				//t0的重载值;1MS,1000US,(65535-1000/((1/11.0592)))=54475D=D4CB
	TH0 = 0xD4;			
	TR0 = 1;
	 
	gBFg_1ms = 1;								//SET 1MS FLAG USE LED FLASHING 
	gBFg_Uart0_1ms_ack = 1;

	gBFg_ScanButton_1ms = 1;
	
	if(loop_20ms_exit > 40)                     //40ms ,接收到正确数据到发送需20.4MS，接收到发送间隔是800US
	{

	}
	else if(loop_20ms_exit == 40)               //40ms
	{
//20150727 TXD0 
    P3 	 &= Bin(11111101);						// 0 level
//20150727 TXD0 
        loop_20ms_exit++;
	}
	else
	{
        loop_20ms_exit++;
	}

	LEDProc();
}

///////////////////////////////////////////////////////////////////////////////
//外部中断1服务程序:
void EX1_INT(void)    interrupt 2
{	
	IE1 = 0;	   					//TCON.1
	EX1 = 0;		  				//IE2=EXF0.0,外部中断2中断请求标志

}


///////////////////////////////////////////////////////////////////////////////
//定时中断1服务程序:
void Timer1_INT(void) interrupt 3
{	
  TF1 = 0;	   							//TCON.7 由硬件自动清零
	TR1 = 0;
	
}



///////////////////////////////////////////////////////////////////////////////
//EUART中断服务程序:
void EUART0_INT(void) interrupt 4          			//485中断
{
	unsigned char i,checksum=0;

	PCON &=0x0BF;								    //设置SCON 为STM0,STM1模式

	if(TI)											//transmit work end event.
	{
		TI = 0;

		if(1 == gbFg_Uart0SendRec)					//gbFg485Send_Rcv==1 allowed send ,stop receive
		{			
			if(gbUart0Len < gbUart0TxdLen)
			{
				ACC	= gbUart0Buff[gbUart0Len];
				TB8	= P;									// even check!       9600 11位,even
				SBUF = gbUart0Buff[gbUart0Len];
				gbUart0Len++;
			}
			else
			{
				for(i=0;i<32;i++)		 			//发送完把接收的BUFFER清空
				{
					gbUart0Buff[i] = 0;
				}
				gbUart0Len = 0;
				gbUart0TxdLen = 0;
				gbFg_Uart0SendRec = 0;   			//gbFg485Send_Rcv==0 allowed receive ,stop send
				REN = 1;

//20150727 TXD0 
    P3 	 &= Bin(11111101);
//20150727 TXD0 
			}
		}
	}	

	if(RI)											//receive one byte event.
	{
		RI = 0;

		if(0 == gbFg_Uart0SendRec)					//gbFg485Send_Rcv==0 allowed receive ,stop send
		{
			ACC	= SBUF;
			if(RB8==P)								// even check!
			{
				gb500msCnt485	= 0;				//UART485字节间隔500MS计数器清零

				loop_20ms_exit = 0;					//字节间隔20MS计数器清零
//20150727 TXD0 
    P3 	 |= Bin(00000010);
//20150727 TXD0


				gbUart0Buff[32] = SBUF;
				for(i=0;i<32;i++)					//先进先出的队列
				{
					gbUart0Buff[i] = gbUart0Buff[i+1];
				}
 
#if HEAD_CODE_FA_EN
				for(i=0;i<32;i++)							//查找满足数据长度和校验和的队列BUFF段
				{
					gbUart0Head = i;
					if((gbUart0Buff[i] >= 3) && (gbUart0Buff[i] <= 29))
					{
						if((gbUart0Buff[i-2] == 0xFA) && (gbUart0Buff[i-1] == 0xFA)) 
						{
							if(gbUart0Buff[i+gbUart0Buff[i]-1] == CalCheckSum1(&gbUart0Buff,i,gbUart0Buff[i]))
							{
								gbFg_Uart0RxEnd = 1; 			//one frame receive ok!!!
								gbFg_Uart0Ack = 0;
								gbFg_Uart0SendRec = 1;
								gb500msCnt485	= 0;			//UART485字节间隔500MS计数器清零
								REN = 0;					
								break;
							}
							else
							{
								gbUart0Len = 0;
								gbUart0TxdLen = 0;
								gbFg_Uart0RxEnd = 0;
								gbFg_Uart0SendRec = 0;    		//gbFg485Send_Rcv==0 allowed receive ,stop send
								gb500msCnt485	= 0;			//UART485字节间隔500MS计数器清零
							}
						}
						else 
						{
							if(gbUart0Buff[i-1] == 0xFA) 
							{
								if(gbUart0Buff[i+gbUart0Buff[i]-1] == CalCheckSum1(&gbUart0Buff,i,gbUart0Buff[i]))
								{
									gbFg_Uart0RxEnd = 1; 			//one frame receive ok!!!
									gbFg_Uart0Ack = 0;
									gbFg_Uart0SendRec = 1;
									gb500msCnt485	= 0;			//UART485字节间隔500MS计数器清零
									REN = 0;					
									break;
								}
								else
								{
									gbUart0Len = 0;
									gbUart0TxdLen = 0;
									gbFg_Uart0RxEnd = 0;
									gbFg_Uart0SendRec = 0;    		//gbFg485Send_Rcv==0 allowed receive ,stop send
									gb500msCnt485	= 0;			//UART485字节间隔500MS计数器清零
								}
							}
						}
					}
					else
					{
						 gbUart0Len = 0;
						 gbUart0TxdLen = 0;
						 gbFg_Uart0RxEnd = 0;
						 gbFg_Uart0SendRec = 0;    			//gbFg485Send_Rcv==0 allowed receive ,stop send
						 gb500msCnt485	= 0;				//UART485字节间隔500MS计数器清零
					}
				}

#else
				for(i=0;i<32;i++)					//查找满足数据长度和校验和的队列BUFF段
				{
					gbUart0Head = i;
					if((gbUart0Buff[i] >= 3) && (gbUart0Buff[i] <= 29))
					{
						if(gbUart0Buff[i+gbUart0Buff[i]-1] == CalCheckSum1(&gbUart0Buff,i,gbUart0Buff[i]))
						{
						    gbFg_Uart0RxEnd = 1; 	//one frame receive ok!!!
							gbFg_Uart0Ack = 0;
							gbFg_Uart0SendRec = 1;
							gb500msCnt485	= 0;	//UART485字节间隔500MS计数器清零
							REN = 0;
							break;
						}
					}
					else
					{
						gbUart0Len = 0;
						gbUart0TxdLen = 0;
						gbFg_Uart0RxEnd = 0;
						gbFg_Uart0SendRec = 0;    			//gbFg485Send_Rcv==0 allowed receive ,stop send
						gb500msCnt485	= 0;				//UART485字节间隔500MS计数器清零
					}
				}
#endif
		   }
		   else
		   {
				gbUart0Len = 0;
				gbUart0TxdLen = 0;
				gbFg_Uart0RxEnd = 0;
				gbFg_Uart0SendRec = 0;    			//gbFg485Send_Rcv==0 allowed receive ,stop send
				gb500msCnt485	= 0;				//UART485字节间隔500MS计数器清零
				REN = 1;				
		   }  
		}	  	
	}
}

///////////////////////////////////////////////////////////////////////////////
//定时中断2服务程序:
void Timer2_INT(void) interrupt 5
{	
	EXF2=0;
	TF2=0; 		   									//EXF2=T2CON.6 TF2=T2CON.7
 
}

///////////////////////////////////////////////////////////////////////////////
//AD中断服务程序:
void ADC_INT(void)  interrupt 6
{
  	ADCON&=bin(10111111);	     					//ADCIF=0;
}

///////////////////////////////////////////////////////////////////////////////
//外部中断2服务程序:
void EX2_INT(void)  interrupt 9
{	
	EXF0 = bin(00000100);		  					//IE2=EXF0.0,清外部中断2中断请求标志，BIT3，BIT2，=01,下降沿触发
	IEN1 &= bin(11111011);							//BIT2,EX2 = 0,关闭外部中断2
	TR1 = 0;										//开定时器2				

}

/****************************************************************************
 * Function Description:the interrupt entrance : Timer4_INT
 * Input parameter     :
 * Output paramter     :void
 ****************************************************************************/
void SCM_INT(void)  interrupt 11
{	
	CLKCON&=bin(11101111);	  						//SCMIF  //必须由硬件自动清零

}

///////////////////////////////////////////////////////////////////////////////
//PWM中断服务程序:
void PWM_INT(void) interrupt 12
{	 
     PWMCON&=bin(11111101);	   						//PWMIF=0;

}

///////////////////////////////////////////////////////////////////////////////
//LPD中断服务程序:
void ELPD_INT(void) interrupt 14
{	
	LPDCON&=bin(10111111);     						//LPDF=0;

}	