#include <intrins.h>
#include <SH79F084B.h>			  //SH79F166A.H	


#include "define.h"


//bit ReadEEprom(U08 *buff,U08 pagenum,U08 gbHeadAddr,U08 len); 
//bit WriteEEprom(U08 *buff,U08 pagenum,U08 gbHeadAddr,U08 len,U08 FlashLock);


//////////////////////////////////////////////////////////////////////////////////////
unsigned char Read_char(U08 pagenum,U08 address)
{
	unsigned char xdata temp;
		
	FLASHCON=1;
	temp=*(unsigned char code *)((pagenum<<8)|address);
   	FLASHCON=0;
	return(temp);
}



//////////////////////////////////////////////////////////////////////////////////////
bit ReadEEprom(U08 *buff,U08 pagenum,U08 gbHeadAddr,U08 len) 		//pagenum<2
{	
	bit ReadErrFlag = 0;
    unsigned char data i = 0;
	
    if(pagenum > 1)
	{
		ReadErrFlag = 1;
		return(ReadErrFlag);
	}
	
	for (i=0; i<len; i++)
    {
		buff[i] = Read_char(pagenum,((gbHeadAddr+i)));
	} 	
	ReadErrFlag = 0;
	return(ReadErrFlag);
}


//////////////////////////////////////////////////////////////////////////////////////
void EEPROMSectorErase(U08 pagenum,U08 FlashLock)			//25BYTE*2PAGE	pagenum is 0,1
{	
	bit  EABak = 0;
	
    EABak = EA;    
    EA = 0; 
	FLASHCON = 0x01;				//eeprom 
	IB_CON1 = 0xE6;				   	// =E6,Sector Erase
	IB_CON2 = 0x05;
	IB_CON3 = 0x0A;
	IB_CON4 = 0x09;
	if(FlashLock != 0xA5)			//Flash erase flag  
	{
		IB_CON2 = 0;
		goto Erase_Err;
	}
	XPAGE = pagenum;	   			//must be const number :  XPAGE = 0x00/0x01
	IB_CON5 = 0x06;
	_nop_();
	_nop_();
	_nop_();
	_nop_();
	_nop_();
Erase_Err:
	FlashLock = 0x00;	
	XPAGE = 0x00;
	FLASHCON = 0x00;	
	EA = EABak;
}




//////////////////////////////////////////////////////////////////////////////////////
void Write_char(U08 pagenum,U08 address,U08 uchardata,U08 FlashLock)
{
	bit  EABak = 0;
	
	if(pagenum > 1)
	{
		goto Write_Err;
	}
//step 1
    EABak = EA; 
	EA = 0;							
	FLASHCON = 0x01;
	IB_OFFSET = address;		
//step 4
	IB_DATA	 = uchardata;		      	
//step 5
	IB_CON1	 = 0x6E;				
	IB_CON2	 = 0x05;				
	IB_CON3	 = 0x0A;
	IB_CON4	 = 0x09;
	if(FlashLock != 0xA5)			   	//FLASH Write Flag
	{
		IB_CON2 = 0;
		goto Write_Err;
	}
	XPAGE = pagenum;					//0=PAGE0;1=PAGE1
	IB_CON5	 = 0x06;
//step 6
	_nop_();							
	_nop_();
	_nop_();
	_nop_();
	_nop_();
Write_Err:
	FlashLock = 0x00;
	FLASHCON = 0x00;
	XPAGE = 0x00;
	EA = EABak;
}



//////////////////////////////////////////////////////////////////////////////////////
bit Write_char_check(U08 pagenum,U08 address,U08 uchardata,U08 FlashLock)
{
	bit  EABak = 0;
	bit gbWriteOkFlag = 0;


    Write_char(pagenum,address,uchardata,FlashLock);

    if(uchardata != Read_char(pagenum,address))
	{
		gbWriteOkFlag = 0;
	}
	else
	{
		gbWriteOkFlag = 1;							//write ok
	}	
	
	return(gbWriteOkFlag);
}


//////////////////////////////////////////////////////////////////////////////////////
bit WriteEEprom(U08 *buff,U08 pagenum,U08 gbHeadAddr,U08 len,U08 FlashLock)
{
	bit gbEepromErrFlag = 0;
	bit gbWriteOkFlag = 0;
	bit gbWriteOkFlag1 = 0;	
		
	unsigned char data i = 0,j = 0,ErrCount = 0;


    if(pagenum > 1)
	{	
		gbEepromErrFlag = 1;
		return(gbEepromErrFlag);
	}

	EEPROMSectorErase(pagenum,FlashLock);				//擦除对应page

	ErrCount = 0;

//write data to eeprom				
	for(i=0; i<len; i++,gbHeadAddr++)
    {	
	    RSTSTAT &= 0xF8;	 							//cls WDT 2048m
		if(1 == Write_char_check(pagenum,gbHeadAddr,buff[i],FlashLock))
		{
			gbWriteOkFlag = 1;
			ErrCount = 0;
		}
		else
		{
			gbWriteOkFlag = 0;

		    if(ErrCount < 10)
			{
			    if((i > 0) && (gbHeadAddr > 0))
				{
			    	i--;
					gbHeadAddr--;
				}
			}

			ErrCount++;
			if(ErrCount >= 10)			  //如果某个字节10都写回读错就跳过改字节继续写下一字节，只到写完255字节为止，且返回写报错错标志
			{
				ErrCount = 10;
			    gbWriteOkFlag1 = 0;
			}
		}
	}	

	if((1==gbWriteOkFlag) || (1==gbWriteOkFlag1))
	{
		gbEepromErrFlag = 1;
	}
	else
	{
		gbEepromErrFlag = 0;
	}

	return(gbEepromErrFlag);				
}

