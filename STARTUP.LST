A51 MACRO ASSEMBLER  STARTUP                                                              06/02/2025 21:39:51 PAGE     1


MACRO ASSEMBLER A51 V8.2.7.0
OBJECT MODU<PERSON> PLACED IN STARTUP.OBJ
ASSEMBLER INVOKED BY: D:\Keil_v5\C51\BIN\A51.EXE STARTUP.A51 SET(SMALL) DEBUG EP

LOC  OBJ            LINE     SOURCE

                       1     $nomod51 
                       2     ;------------------------------------------------------------------------------
                       3     ;  This file is part of the C51 Compiler package
                       4     ;  Copyright (c) 1988-2005 Keil Elektronik GmbH and Keil Software, Inc.
                       5     ;  Version 8.01
                       6     ;
                       7     ;  *** <<< Use Configuration Wizard in Context Menu >>> ***
                       8     ;------------------------------------------------------------------------------
                       9     ;  STARTUP.A51:  This code is executed after processor reset.
                      10     ;
                      11     ;  To translate this file use A51 with the following invocation:
                      12     ;
                      13     ;     A51 STARTUP.A51
                      14     ;
                      15     ;  To link the modified STARTUP.OBJ file to your application use the following
                      16     ;  Lx51 invocation:
                      17     ;
                      18     ;     Lx51 your object file list, STARTUP.OBJ  controls
                      19     ;
                      20     ;------------------------------------------------------------------------------
                      21     ;
                      22     ;  User-defined <h> Power-On Initialization of Memory
                      23     ;
                      24     ;  With the following EQU statements the initialization of memory
                      25     ;  at processor reset can be defined:
                      26     ;
                      27     ; <o> IDATALEN: IDATA memory size <0x0-0x100>
                      28     ;     <i> Note: The absolute start-address of IDATA memory is always 0
                      29     ;     <i>       The IDATA space overlaps physically the DATA and BIT areas.
  0080                30     IDATALEN        EQU     80H
                      31     ;
                      32     ; <o> XDATASTART: XDATA memory start address <0x0-0xFFFF> 
                      33     ;     <i> The absolute start address of XDATA memory
  0000                34     XDATASTART      EQU     0     
                      35     ;
                      36     ; <o> XDATALEN: XDATA memory size <0x0-0xFFFF> 
                      37     ;     <i> The length of XDATA memory in bytes.
  0000                38     XDATALEN        EQU     0      
                      39     ;
                      40     ; <o> PDATASTART: PDATA memory start address <0x0-0xFFFF> 
                      41     ;     <i> The absolute start address of PDATA memory
  0000                42     PDATASTART      EQU     0H
                      43     ;
                      44     ; <o> PDATALEN: PDATA memory size <0x0-0xFF> 
                      45     ;     <i> The length of PDATA memory in bytes.
  0000                46     PDATALEN        EQU     0H
                      47     ;
                      48     ;</h>
                      49     ;------------------------------------------------------------------------------
                      50     ;
                      51     ;<h> Reentrant Stack Initialization
                      52     ;
                      53     ;  The following EQU statements define the stack pointer for reentrant
                      54     ;  functions and initialized it:
                      55     ;
                      56     ; <h> Stack Space for reentrant functions in the SMALL model.
                      57     ;  <q> IBPSTACK: Enable SMALL model reentrant stack
                      58     ;     <i> Stack space for reentrant functions in the SMALL model.
A51 MACRO ASSEMBLER  STARTUP                                                              06/02/2025 21:39:51 PAGE     2

  0000                59     IBPSTACK        EQU     0       ; set to 1 if small reentrant is used.
                      60     ;  <o> IBPSTACKTOP: End address of SMALL model stack <0x0-0xFF>
                      61     ;     <i> Set the top of the stack to the highest location.
  0100                62     IBPSTACKTOP     EQU     0xFF +1     ; default 0FFH+1  
                      63     ; </h>
                      64     ;
                      65     ; <h> Stack Space for reentrant functions in the LARGE model.      
                      66     ;  <q> XBPSTACK: Enable LARGE model reentrant stack
                      67     ;     <i> Stack space for reentrant functions in the LARGE model.
  0000                68     XBPSTACK        EQU     0       ; set to 1 if large reentrant is used.
                      69     ;  <o> XBPSTACKTOP: End address of LARGE model stack <0x0-0xFFFF>
                      70     ;     <i> Set the top of the stack to the highest location.
  0000                71     XBPSTACKTOP     EQU     0xFFFF +1   ; default 0FFFFH+1 
                      72     ; </h>
                      73     ;
                      74     ; <h> Stack Space for reentrant functions in the COMPACT model.    
                      75     ;  <q> PBPSTACK: Enable COMPACT model reentrant stack
                      76     ;     <i> Stack space for reentrant functions in the COMPACT model.
  0000                77     PBPSTACK        EQU     0       ; set to 1 if compact reentrant is used.
                      78     ;
                      79     ;   <o> PBPSTACKTOP: End address of COMPACT model stack <0x0-0xFFFF>
                      80     ;     <i> Set the top of the stack to the highest location.
  0100                81     PBPSTACKTOP     EQU     0xFF +1     ; default 0FFH+1  
                      82     ; </h>
                      83     ;</h>
                      84     ;------------------------------------------------------------------------------
                      85     ;
                      86     ;  Memory Page for Using the Compact Model with 64 KByte xdata RAM
                      87     ;  <e>Compact Model Page Definition
                      88     ;
                      89     ;  <i>Define the XDATA page used for PDATA variables. 
                      90     ;  <i>PPAGE must conform with the PPAGE set in the linker invocation.
                      91     ;
                      92     ; Enable pdata memory page initalization
  0000                93     PPAGEENABLE     EQU     0       ; set to 1 if pdata object are used.
                      94     ;
                      95     ; <o> PPAGE number <0x0-0xFF> 
                      96     ; <i> uppermost 256-byte address of the page used for PDATA variables.
  0000                97     PPAGE           EQU     0
                      98     ;
                      99     ; <o> SFR address which supplies uppermost address byte <0x0-0xFF> 
                     100     ; <i> most 8051 variants use P2 as uppermost address byte
  00A0               101     PPAGE_SFR       DATA    0A0H
                     102     ;
                     103     ; </e>
                     104     ;------------------------------------------------------------------------------
                     105     
                     106     ; Standard SFR Symbols 
  00E0               107     ACC     DATA    0E0H
  00F0               108     B       DATA    0F0H
  0081               109     SP      DATA    81H
  0082               110     DPL     DATA    82H
  0083               111     DPH     DATA    83H
                     112     
                     113                     NAME    ?C_STARTUP
                     114     
                     115     
                     116     ?C_C51STARTUP   SEGMENT   CODE
                     117     ?STACK          SEGMENT   IDATA
                     118     
----                 119                     RSEG    ?STACK
0000                 120                     DS      1
                     121     
                     122                     EXTRN CODE (?C_START)
                     123                     PUBLIC  ?C_STARTUP
                     124     
A51 MACRO ASSEMBLER  STARTUP                                                              06/02/2025 21:39:51 PAGE     3

----                 125                     CSEG    AT      0
0000 020000   F      126     ?C_STARTUP:     LJMP    STARTUP1
                     127     
----                 128                     RSEG    ?C_C51STARTUP
                     129     
0000                 130     STARTUP1:
                     131     
                     132     IF IDATALEN <> 0
0000 787F            133                     MOV     R0,#IDATALEN - 1
0002 E4              134                     CLR     A
0003 F6              135     IDATALOOP:      MOV     @R0,A
0004 D8FD            136                     DJNZ    R0,IDATALOOP
                     137     ENDIF
                     138     
                     139     IF XDATALEN <> 0
                                             MOV     DPTR,#XDATASTART
                                             MOV     R7,#LOW (XDATALEN)
                               IF (LOW (XDATALEN)) <> 0
                                             MOV     R6,#(HIGH (XDATALEN)) +1
                               ELSE
                                             MOV     R6,#HIGH (XDATALEN)
                               ENDIF
                                             CLR     A
                             XDATALOOP:      MOVX    @DPTR,A
                                             INC     DPTR
                                             DJNZ    R7,XDATALOOP
                                             DJNZ    R6,XDATALOOP
                             ENDIF
                     153     
                     154     IF PPAGEENABLE <> 0
                                             MOV     PPAGE_SFR,#PPAGE
                             ENDIF
                     157     
                     158     IF PDATALEN <> 0
                                             MOV     R0,#LOW (PDATASTART)
                                             MOV     R7,#LOW (PDATALEN)
                                             CLR     A
                             PDATALOOP:      MOVX    @R0,A
                                             INC     R0
                                             DJNZ    R7,PDATALOOP
                             ENDIF
                     166     
                     167     IF IBPSTACK <> 0
                             EXTRN DATA (?C_IBP)
                             
                                             MOV     ?C_IBP,#LOW IBPSTACKTOP
                             ENDIF
                     172     
                     173     IF XBPSTACK <> 0
                             EXTRN DATA (?C_XBP)
                             
                                             MOV     ?C_XBP,#HIGH XBPSTACKTOP
                                             MOV     ?C_XBP+1,#LOW XBPSTACKTOP
                             ENDIF
                     179     
                     180     IF PBPSTACK <> 0
                             EXTRN DATA (?C_PBP)
                                             MOV     ?C_PBP,#LOW PBPSTACKTOP
                             ENDIF
                     184     
0006 758100   F      185                     MOV     SP,#?STACK-1
                     186     
                     187     ; This code is required if you use L51_BANK.A51 with Banking Mode 4
                     188     ;<h> Code Banking
                     189     ; <q> Select Bank 0 for L51_BANK.A51 Mode 4
                     190     
A51 MACRO ASSEMBLER  STARTUP                                                              06/02/2025 21:39:51 PAGE     4

                             
                             
                                             
                             
                     195     ;</h>
0009 020000   F      196                     LJMP    ?C_START
                     197     
                     198                     END
A51 MACRO ASSEMBLER  STARTUP                                                              06/02/2025 21:39:51 PAGE     5

SYMBOL TABLE LISTING
------ ----- -------


N A M E             T Y P E  V A L U E   ATTRIBUTES

?C_C51STARTUP. . .  C SEG    000CH       REL=UNIT
?C_START . . . . .  C ADDR   -----       EXT
?C_STARTUP . . . .  C ADDR   0000H   A   
?STACK . . . . . .  I SEG    0001H       REL=UNIT
ACC. . . . . . . .  D ADDR   00E0H   A   
B. . . . . . . . .  D ADDR   00F0H   A   
DPH. . . . . . . .  D ADDR   0083H   A   
DPL. . . . . . . .  D ADDR   0082H   A   
IBPSTACK . . . . .  N NUMB   0000H   A   
IBPSTACKTOP. . . .  N NUMB   0100H   A   
IDATALEN . . . . .  N NUMB   0080H   A   
IDATALOOP. . . . .  C ADDR   0003H   R   SEG=?C_C51STARTUP
PBPSTACK . . . . .  N NUMB   0000H   A   
PBPSTACKTOP. . . .  N NUMB   0100H   A   
PDATALEN . . . . .  N NUMB   0000H   A   
PDATASTART . . . .  N NUMB   0000H   A   
PPAGE. . . . . . .  N NUMB   0000H   A   
PPAGEENABLE. . . .  N NUMB   0000H   A   
PPAGE_SFR. . . . .  D ADDR   00A0H   A   
SP . . . . . . . .  D ADDR   0081H   A   
STARTUP1 . . . . .  C ADDR   0000H   R   SEG=?C_C51STARTUP
XBPSTACK . . . . .  N NUMB   0000H   A   
XBPSTACKTOP. . . .  N NUMB   0000H   A   
XDATALEN . . . . .  N NUMB   0000H   A   
XDATASTART . . . .  N NUMB   0000H   A   


REGISTER BANK(S) USED: 0 


ASSEMBLY COMPLETE.  0 WARNING(S), 0 ERROR(S)
