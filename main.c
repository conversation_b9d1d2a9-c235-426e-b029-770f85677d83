#include <constant.H>
#include "define.h"

extern void UartInit(void);
extern void UartBaudSet(unsigned int baudrate);
extern void UartConf(unsigned char state);
extern void UartIntDisable(void);
extern void UartIntEnable(void);
extern void UARTReceiveOn(void);
extern void UARTSendOn(void);


//void Uart0NoAck(void);

void T2Initial(void);
void T3Initial(void); 
void T4Initial(void);
void CLR_DataRAM(void);


#define D_Uart0_ID_HeadAddr    0
#define D_Uart0_ID_PageNum     0
#define D_Uart0_ID_DataLenth   4

#define D_Uart1_ID_HeadAddr    0
#define D_Uart1_ID_PageNum     1
#define D_Uart1_ID_DataLenth   4

unsigned char data gB_Uart0_1ms = 0;

bit gBFg_Uart0_1ms_ack = 0;


//led
bit gBFg_1ms = 0;
unsigned long data LED1_Count = 0;
unsigned long data LED1_On_Count = 0;
unsigned long data LED1_Off_Count = 0;
unsigned int data LED1_On = 0;
unsigned int data LED1_Off = 0;


unsigned long data LED2_Count = 0;
unsigned long data LED2_On_Count = 0;
unsigned long data LED2_Off_Count = 0;
unsigned int data LED2_On = 0;
unsigned int data LED2_Off = 0;


sbit BUTTON = P3^2;

sbit LED1 	= P1^7;
sbit LED2 	= P1^6;


#define LED1_On()      P1 &= bin(01111111) 
#define LED1_Off()     P1 |= bin(10000000)

#define LED2_On()      P1 &= bin(10111111)  
#define LED2_Off()     P1 |= bin(01000000)

#define LED12_On()     P1 &= bin(00111111) 
#define LED12_Off()    P1 |= bin(11000000)


void LEDProc(void);


extern bit gbFg_Uart0SendRec;
extern bit gbFg_Uart0RxEnd;
extern bit gbFg_Uart0Ack;
extern unsigned char xdata gbUart0Head;
extern unsigned char xdata gbUart0Len;
extern unsigned char xdata gbUart0TxdLen;
extern unsigned char xdata gbUart0Buff[MAX_UART_DATA_LEN]; 
extern unsigned char xdata gbUart0Buff1[MAX_UART_DATA_LEN]; 


void Uart0Decode(void);
void Uart0Ack(void);
//void ClsUart0Buff(void);




void T0Initial(void);
void T1Initial(void);

//void INT1Init(void);	
//void INT2Init(void);

bit gBFg_ScanButton_1ms = 0;
unsigned char xdata BUTTON_Buff = 0;
unsigned char xdata BUTTON_New = 0;
unsigned char xdata BUTTON_Old = 0;
unsigned char xdata BUTTON_Value = 0;
unsigned char xdata BUTTON_Value1 = 0;
unsigned char xdata BUTTON_Value_Buff[4];
bit gbFg_BUTTON_Flag = 0;
unsigned char xdata BUTTON_Index = 0;

void ScanButton(void);
//unsigned char GetButtonChangeStatus(void);
//unsigned char GetButtonStatus(void);
unsigned char CalCheckSum(unsigned char *buff,unsigned char buffHeadAdd,unsigned char Len);


unsigned char xdata  ButtonID;				//1 byte
unsigned char xdata  UpOrDown;				//1 byte

unsigned char xdata  Lenth;				    //1 byte	=5 byte
unsigned char xdata  OBJ_CMD;				//1 byte	=0x60
unsigned char xdata  Check;				    //1 byte


#define ERR_AckCommand				 0xA5 						//???????


#define PEEK_Command				 0xC3 						//???
#define WITHDRAW_Command			 0xC5						//???
#define OBJ_Changed 				 0x31
#define Button_Status                0x32
#define Enquire_Button_Status        0x33

#define Enquire_Row_Status           0x21			   			//???
#define Enquire_OBJ_ID 				 0x22
#define Set_OBJ_ID 					 0x23
#define Set_OBJ_LED 				 0x24
#define Get_OBJ_LED_Status 			 0x25



unsigned char xdata gBFg_100ms = 0;

bit gb_TestMode = 0;
unsigned xdata gb_TestModeCount3s = 0;


//////////////////////////////////////////////////////////////////////////////
void CLR_DataRAM(void)
{
	unsigned char *p;
	unsigned char i;

	p = 0x00;										//p = &temp_Data;
	for(i=0x00; i<0xfe; p++,i++)	*p=0;			//clear the map RAM
}	

//////////////////////////////////////////////////////////////////////////////					   
/*
void CLR_XdataRAM(void)
{
		unsigned char *p;
		unsigned char i;

		p = 0x00;	//p = &temp_Xdata;
		for(i=0x00; i<0xfe; p++,i++)	*p=0;		//clear the map RAM
}
*/

//////////////////////////////////////////////////////////////////////////////
void SysInit(void)
{
	EA	=	0; 				  		//???ж???

	P1 = bin(11000000);       		//???IO??????0
	P3 = bin(00000000);
	P4 = bin(00000000);		         

	P1M0=bin(00111100);		  		//00:?????? 01:???? 10:???? 11:??????
	P1M1=bin(11000011); 	  		//p1.2 p1.3???????,?????.P1.3=RXD,P1.2=TXD,P1.6??P1.7 IS LED,LOW LEVEL ENABLE 

	P3M0=bin(00000000);		  		//p3.0 p3.1???????,?????.P3.0=RXD,P3.1=TXD	 P3.2 KEY
	P3M1=bin(11111111);		  

	P4M0=bin(00000000);		  
	P4M1=bin(11111111);		 
	
//---------------?????
	CLKCON = bin(00000000);   		//bit6~5 ??????? 10--4???8M/4=2MHZ  01--2???  	//P25
							  		//bit7--32.768kHz????г??????????????λ
							  		//bit4--?????????λ(SCMIF)  0?????????????????? P27
							  		//bit3-- OSCXCLK???????????	 bit2--??????λ
							  		//bit1/0--???

//?????
	IPL0 = bin(00001000);
	IPL1 = bin(000010000);
	IPH0 = bin(00000000);
	IPH1 = bin(00000000);


//?????0?????1mS:
    T0Initial();	
 
//T1????5mS
  	T1Initial(); 


//T2????5mS
  	//T2Initial();
//---------------?ж?				  
//?ж?????:
	IEN0=bin(10011010);	  			//bit0:??EX0; bit1:???0_ET0; bit3:???1_TE1;bit4??UART0 bit7:?????ж?_EA;	???


//---------------???????
 	LPDCON=0x00;					//??????????????  P73

//---------------PWM ???
  PWMCON=bin(00000000);			//bit0:pwm??????? bit1:PWM?ж??? bit7??PWM??????
	                        		//bit4-5????????? 00--?????/2 /4/8/16  0.5*2=1uS*255=4KHz
	PWMP=250;
//	PWMD=125;
//***************a/d????:
	ADCON=bin(10000001);			//bit7--??ADC??飬bit1~3--?????AN0??bit0--??????AD????????????????????
	ADT=bin(01010011);				//adc???????  bit7-5:adc??????????λ bit3-0:??????????λ;	
									//ad???????101:16Tsys=16*8M=2uS; ad???????12*2uS+(3+1)*2uS=32uS;
	ADCH=0x01;//81; 				//0x11 ad??????hit1:AN0??AN7; //AN4;

	CLR_DataRAM();
//	CLR_XdataRAM();
}


//////////////////////////////////////////////////////////////////////////////
//void INT2Init(void)								//INT2,?????????ж?
//{
//	EXF0 = bin(00000100);		  				//IE2=EXF0.0,???ж?2?ж???????,
//	IEN1 |= bin(00000100);						//BIT2,EX2 = 0,??????ж?2
//}

/////////////////////////////////////////////////////////////////////////////
//void INT1Init(void)								//INT1,?????????ж?
//{
//	#if INT_EX1_EN
//	IT1 = 1;									//INT1???????????????? 
//	IE1 = 0;	   								//INT1???ж???????TCON.1
//	EX1 = 1;		  							//INT1???ж???????
//    P3 |= bin(10000000);
//	#else
//	IT1 = 0;									//INT1???????????????? 
//	IE1 = 0;	   								//INT1???ж???????TCON.1
//	EX1 = 0;		  							//INT1???ж???????
//    P3 &= bin(01111111);
//	#endif	
//}


/////////////////////////////////////////////////////////////////////////////////////////
void UseDataInit(void)
{
	unsigned char i;

	gbUart0Len = 0;
	gbUart0TxdLen = 0;
	gbFg_Uart0SendRec = 0;    					//gbFg_Uart0SendRec==0 allowed receive ,stop send
	REN = 1;
	for(i=0;i<32;i++)	
	{			
		gbUart0Buff[i] = 0x00;
	}

	LED1_On = 0;
	LED1_Off =0;
	LED2_On = 0;
	LED2_Off =0;

	gbFg_BUTTON_Flag = 0;
	BUTTON_Index = 0;

 	BUTTON_Buff = 0;
	BUTTON_New = 0;
	BUTTON_Old = 0;
	BUTTON_Value = 0;
  BUTTON_Value1 = 0;
	BUTTON_Value_Buff[0] = 0;
	BUTTON_Value_Buff[1] = 0;
	BUTTON_Value_Buff[2] = 0;
	BUTTON_Value_Buff[3] = 0;

	if(BUTTON == 0)
	{
	   BUTTON_Buff = 0;
	 	 BUTTON_New = 0;									//button is presss
	 	 BUTTON_Old = 0;
		 BUTTON_Value1 = 0;
	}
	else
	{
		 BUTTON_Buff = 1;
	 	 BUTTON_New = 1;
	 	 BUTTON_Old = 1;
     BUTTON_Value1 = 1;	
	}
}



//////////////////////////////////////////////////////////////////////////////
void T0Initial(void)	
{
	TCON &= bin(11001111);			//bit4:???????0  bit5:?????0?????? 
	                        		//bit0:1?INT0???????? 0????????? 
	TCON1 &= bin(01001010); 		//?????????????????;
	TCON1 |= bin(00000100); 				 
									//bit2-3??????:1--?????????  0.5uS;  0--?????12???
									//bit5-6?????????,0??????; 1?32768KHz???
	TMOD &= bin(11110000);			//bit0-1:??0??????;10:8λ???????
	TMOD |= bin(00000001);  		//16λ???????
	                        		//bit2:??0??? 0:???????	1:????????
									//bit3:
	TL0 = 0xCB;			    		//t0????????????;	   1MS,1000US,(65535-1000/((1/11.0592)))=54475D=D4CB
	TH0 = 0xD4;			    		//t0???????;	 250*0.5uS=125uS*2=4KHz
	TF0 = 0;
	TR0 = 1;	 
}


//////////////////////////////////////////////////////////////////////////////
void T1Initial(void)	
{
	TCON &= bin(00111111);			//bit4:???????1  bit5:?????0?????? 
	                        		//bit0:1?INT0???????? 0????????? 
	TCON1 &= bin(00100101); 		//?????????????????;
	TCON1 |= bin(00001000); 				 
									//bit2-3??????:1--?????????  0.5uS;  0--?????12???
									//bit5-6?????????,0??????; 1?32768KHz???
	TMOD &= bin(00001111);			//bit0-1:??0??????;10:8λ???????
	TMOD |= bin(00010000);  		//16λ???????
	                        		//bit2:??0??? 0:???????	1:????????
									//bit3:
	TL1 = 0x81;			    		//t0????????????;	   104us,(65535-104/((1/11.0592)))=64385D=FB81
	TH1 = 0xFB;			  
	TF1 = 0;
	TR1 = 1;	 
}


//////////////////////////////////////////////////////////////////////////////
void InitLedTestMode(void)
{
    if(gb_TestMode == 0)						 //
	{
		gb_TestMode = 1;						 //set led test mode flag
		gb_TestModeCount3s = 0;
		LED1_Off();							     //0ff	
		LED1_Count = 0;
		LED2_Off();							     //0ff	
		LED2_Count = 0;
	}
}


//////////////////////////////////////////////////////////////////////////////
void LEDProc(void)
{
	if(gBFg_1ms == 1)
	{
		gBFg_1ms = 0;

		gBFg_100ms++;
		if(gBFg_100ms >= 100)
		{
			gBFg_100ms = 0;

//test 1/10s = 100ms//////////////////////////////////////////////////////////
//			LED1 = ~LED1;
//			LED2 = ~LED2;
//test 1/10s = 100ms//////////////////////////////////////////////////////////

//TEST LED MODE,AFTER WORK 3S,EXIT TEST MODE
            if(gb_TestMode == 1)
			{
			    gb_TestModeCount3s++;
				if(gb_TestModeCount3s >= 30)						//3s
				{
				    gb_TestModeCount3s = 0;
					gb_TestMode = 0;
				}
				else
				{	
				   LED1_Count++;
				   if(LED1_Count <= 5)						//LED1_On_Count)
				   {
						LED12_On();							//0n
				   }
				   else if(LED1_Count < 10)					//LED1_Off_Count)
				   {
				        LED12_Off();							//0ff
				   }
				   else if(LED1_Count == 5)					//LED1_Off_Count)
				   {
				        LED12_Off();							//0ff
				   		LED1_Count = 0;
				   }
				   else
				   {
				   		LED1_Count = 0;
				   }					 
				}    
			}
//TEST LED MODE,AFTER WORK 3S,EXIT TEST MODE
			else
			{
				if(LED1_On == 0)								//LED1
				{
					LED12_Off();							        //0ff	
					LED1_Count = 0;
				}
				else if((LED1_On == 0) && (LED1_Off == 0))
				{
					LED12_Off();							        //0ff	
					LED1_Count = 0;
				}
				else
				{
					if(LED1_Off == 0)							//LED1
					{
						LED12_On();								//0n	
						LED1_Count = 0;
					}
					else
					{
					   LED1_Count++;
					   if(LED1_Count <= LED1_On_Count)
					   {
							LED12_On();							//0n
					   }
					   else if(LED1_Count < LED1_Off_Count)
					   {
					        LED12_Off();							//0ff
					   }
					   else if(LED1_Count == LED1_Off_Count)
					   {
					        LED12_Off();							//0ff
					   		LED1_Count = 0;
					   }
					   else
					   {
					   		LED1_Count = 0;
					   }
					}
			    }
			}
		}
	}
}



///////////////////////////////////////////////////////////////////////////////
//unsigned char GetButtonStatus(void)
//{
//	unsigned char xdata temp = 0;

//	P3M0 &= bin(11111011);		  						//P3.2 KEY
//	P3M1 &= bin(11111011);		  
//	P3 |= bin(00000100);
//	_nop_();
//	_nop_();
//	_nop_();
//	if(BUTTON == 0)
//	{
//	 	 temp = 1;										//button is presss
//	}
//	else
//	{
//	 	 temp = 2;										//button is relex
//	}
//	return(temp);
//}

///////////////////////////////////////////////////////////////////////////////
//unsigned char GetButtonChangeStatus(void)
//{
//	unsigned char xdata temp = 0;


//	if(gbFg_BUTTON_Flag == 1)				  //?????????????????????????δ?????????????????????????????
//	{
//		if(BUTTON_Index <= 3)
//		{
//			BUTTON_Value = BUTTON_Value_Buff[BUTTON_Index];	
//			BUTTON_Index++;
//			if(BUTTON_Index > 3)
//			{
//				gbFg_BUTTON_Flag = 0;
//				BUTTON_Index = 0;
//				BUTTON_Value_Buff[0] = 0;
//				BUTTON_Value_Buff[1] = 0;
//				BUTTON_Value_Buff[2] = 0;
//				BUTTON_Value_Buff[3] = 0;
//			}
//			temp = BUTTON_Value;
//		}
//		else
//		{
//			gbFg_BUTTON_Flag = 0;
//			BUTTON_Index = 0;
//			BUTTON_Value_Buff[0] = 0;
//			BUTTON_Value_Buff[1] = 0;
//			BUTTON_Value_Buff[2] = 0;
//			BUTTON_Value_Buff[3] = 0;
//			temp = BUTTON_Value1;
//		}
//	}
//	else   																//???????????δ????????
//	{
//		BUTTON_Value = BUTTON_Value_Buff[BUTTON_Index];	
//	    temp = BUTTON_Value;

//	    if((BUTTON_Index == 2) && (BUTTON_Value1 == 3))					//???????????????????????1
//		{

//		}
//		else
//		{
//		    if((BUTTON_Index == 0)  && (BUTTON_Value1 == 4))	  		//???????????????????????1
//			{

//			}
//			else
//			{
//				BUTTON_Index++;
//				if(BUTTON_Index > 3)									//????????????????????????????1
//				{
//					gbFg_BUTTON_Flag = 0;
//					BUTTON_Index = 0;
//					BUTTON_Value_Buff[0] = 0;
//					BUTTON_Value_Buff[1] = 0;
//					BUTTON_Value_Buff[2] = 0;
//					BUTTON_Value_Buff[3] = 0;
//				}
//			}
//		}
//	}
//	return(temp);													    //????????
//}



/////////////////////////////////////////////////////////////////////////////
void ScanButton(void)
{
	unsigned char xdata temp = 0;

	if(gBFg_ScanButton_1ms == 1)
	{
		gBFg_ScanButton_1ms = 0;

		P3M0 &= bin(11111011);		  							//P3.2 KEY
		P3M1 &= bin(11111011);		  
		P3 |= bin(00000100);
		_nop_();
		_nop_();
		_nop_();
	
	
		if(BUTTON == 0)
		{
		 	 BUTTON_Buff = 0;									//button is presss
		}
		else
		{
		 	 BUTTON_Buff = 1;									//button is relex
		}
	
		if(BUTTON_New != BUTTON_Buff)
		{
			BUTTON_New = BUTTON_Buff;
		}
		else	   												//???????
		{
			if(BUTTON_Old == BUTTON_New)
			{
				if(BUTTON_New == 0)
				{
					BUTTON_Value_Buff[2] = 3;
			 		BUTTON_Value1 = 3;										//button is long presss
				}
				else
				{
					BUTTON_Value_Buff[0] = 4;
			 		BUTTON_Value1 = 4;										//button is long relex
				}
			}
			else
			{
				if(BUTTON_New == 0)
				{
					BUTTON_Value_Buff[1] = 1;
			 		BUTTON_Value1 = 1;										//button is presss
				}
				else
				{
					BUTTON_Value_Buff[3] = 2;
			 		BUTTON_Value1 = 2;										//button is relex
	
					if(BUTTON_Index == 0)
					{
						gbFg_BUTTON_Flag = 1;
						BUTTON_Index = 0;
					}
					else
					{
						gbFg_BUTTON_Flag = 1;
					}
				}
				BUTTON_Old = BUTTON_New;
			}
		}
	}
}


/////////////////////////////////////////////////////////////////////////////
unsigned char CalCheckSum(unsigned char *buff,unsigned char buffHeadAdd,unsigned char Len)
{
	unsigned char data temp,i;
	
	temp = 0;
	for(i=buffHeadAdd;i<(buffHeadAdd+Len-1);i++)
	{
		temp = 	temp + buff[i];
	}
	return(temp);
}



//////////////////////////////////////////////////////////////////////////////////////////
//void ClsUart0Buff(void)
//{
//	unsigned char i;
//		
//	for(i=0;i<32;i++)		 		//???????????BUFFER???
//	{
//		gbUart0Buff[i] = 0;
//	}
//	gbUart0Len = 0;
//	gbUart0TxdLen = 0;
//	gbFg_Uart0SendRec = 0;   		//gbFg_Uart0SendRec==0 allowed receive ,stop send
//	REN = 1;
//	
//	gbUart0Head = 0;
//}


//void Uart0NoAck(void)
//{
//    unsigned char i;

//	for(i=0;i<32;i++)		 			//???????????BUFFER???
//	{
//		gbUart0Buff[i] = 0;
//	}
//	gbUart0Len = 0;
//	gbUart0TxdLen = 0;
//	gbFg_Uart0SendRec = 0;   			//gbFg485Send_Rcv==0 allowed receive ,stop send
//	REN = 1;
//}

/****************************************************************************
 *						Uart Monitor
 * Function Description:process OBJ protocol.
 * Input parameter     :void
 * Output paramter     :void
 * Remarks	       	   :RS485
 ****************************************************************************/
void Uart0Decode(void)	
{

	if(1 == gbFg_Uart0RxEnd) 		 
	{	
		gbFg_Uart0RxEnd = 0;
		
		gB_Uart0_1ms = 0;

		// 简化发送逻辑：只发送一个0x55字节
		gbUart0Buff1[0] = 1;			// 数据长度为1字节
		gbUart0Buff1[1] = 0x55;			// 发送0x55


		gbFg_Uart0Ack = TRUE;			 		//need respon CCB
	}
}


/****************************************************************************
 *						Uart Monitor
 * Function Description:process DLT645 protocol.
 * Input parameter     :void
 * Output paramter     :void
 * Remarks	       :RS485
 ****************************************************************************/
void Uart0Ack(void)
{
	unsigned char i;

	if(TRUE == gbFg_Uart0Ack)
	{
		if(1 == gBFg_Uart0_1ms_ack)
		{
			gB_Uart0_1ms++;
			if(gB_Uart0_1ms > 0)			 			//5
			{
				gB_Uart0_1ms = 0;
				gBFg_Uart0_1ms_ack = 0;
				gbFg_Uart0Ack = 0;	

				// 直接发送0x55，不需要长度字节
				gbUart0TxdLen = 1;					// 发送长度为1字节
				gbUart0Buff[0] = 0x55;				// 直接设置要发送的数据
				
				for (i=0;i<32;i++)
				{
					gbUart0Buff1[i] = 0;
				}
		
				gbFg_Uart0SendRec = 1;	 				//send mode
				gbUart0Len = 0;	
				TB8	= 1;
		    TI = 1;
			}
		}
	}
}


//测试的发送函数
void UART_SendByte(unsigned char Byte)
{
	SBUF=Byte;
	while(TI==0);
	TI=0;
}

/////////////////////////////////////////////////////////////////////////////////////////
void main(void)
{
	EA = 0;			       		 				//???ж?
	UartIntDisable();
	UseDataInit();
	SysInit(); 	
 	UartInit();
	UartBaudSet(9600);	
	UartConf(D_R_On);							//????,????????
	UartIntEnable();
	
//	INT1Init();
//	INT2Init();

	gb_TestMode = 0;
  	//InitLedTestMode();
	
	EA = 1;										//?????ж?

	while(1)
	{   
		RSTSTAT &= 0xF8;

	 	//ScanButton();							//1ms do once

		// 直接发送0x55进行测试
		UART_SendByte(0x55);

		// 添加延时避免发送过快
		{
			unsigned int delay;
			for(delay = 0; delay < 10000; delay++);
		}
   }
}