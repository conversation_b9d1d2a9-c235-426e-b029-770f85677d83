
typedef unsigned char U08;

//////////////////////////////////////////////////////////////////////////////////////////////////
//一个字节十六进制转成二进制
#define LongToBin(n) \						
(                    \
((n >> 21) & 0x80) | \
((n >> 18) & 0x40) | \
((n >> 15) & 0x20) | \
((n >> 12) & 0x10) | \
((n >>  9) & 0x08) | \
((n >>  6) & 0x04) | \
((n >>  3) & 0x02) | \
((n      ) & 0x01)   \
)
//一个字节十六进制转成二进制表示
#define Bin(n) LongToBin(0x##n##l)	//write binary charactor set,exsample : Bin(11111111) = 0xff


#define	MAKEDWORD(v1,v2,v3,v4)	(((unsigned long)(v1)<<24) + ((unsigned long)(v2)<<16)+ ((unsigned long)(v3)<<8)+ (unsigned long)(v4))
#define	MAKEWORD(v1,v2)			(((unsigned int)(v1)<<8) + (unsigned int)(v2))
#define	HIBYTE(v1)				((unsigned char)((v1)>>8))
#define	LOBYTE(v1)				((unsigned char)((v1)&0xff))


#define BIT0                	(0x0001) 
#define BIT1                	(0x0002)
#define BIT2                	(0x0004)
#define BIT3                	(0x0008)
#define BIT4                	(0x0010)
#define BIT5                	(0x0020)
#define BIT6                	(0x0040)
#define BIT7               	 	(0x0080)
#define BIT8                	(0x0100) 
#define BIT9                	(0x0200)
#define BIT10               	(0x0400)
#define BIT11               	(0x0800)
#define BIT12               	(0x1000)
#define BIT13               	(0x2000)
#define BIT14               	(0x4000)
#define BIT15               	(0x8000)



#define UART_CH1           		0      
#define UART_CH2           		1
#define UART_CH3           		2

#define D_R_On    				0
#define D_R_Off    				1
#define D_S_On    				2
#define D_S_Off    				3
#define D_R_Off_S_Off    		4



//RS485 uart config
#define	FSYSCLK						1660000				//osc = 16.6M 



#define	CAL_UART_BAUDRATE(X)		(65536-FSYSCLK/16/(X))		//C/T2=1;Set BaudRate with Timer 2 ,Mode 2.
#define	CAL_UART_BAUDRATE1(X)		(65536-FSYSCLK/2/16/(X))	//C/T2=0,TCLKP2=1;Set BaudRate with Timer 2 ,Mode 2.
#define	CAL_UART_BAUDRATE2(X)		(65536-FSYSCLK/2/12/16/(X))	//C/T2=0,TCLKP2=0;Set BaudRate with Timer 2 ,Mode 2.


#define  CAL_UART1_BAUDRATE(X)      ((unsigned int)(32768-(unsigned int)(FSYSCLK/16/(X))))	//波特率计算
#define  CAL_UART1_BAUDRATE1(X)     (unsigned char)((unsigned int)(FSYSCLK/(X) - CAL_UART1_BAUDRATE(X)))	//波特率计算



#define	CAL_TIME0(X)				(65536-(unsigned int)(FSYSCLK/1000000)*(unsigned int)(X))	 
#define	CAL_TIME1(X)				(65536-(unsigned int)(FSYSCLK/1000000)*(unsigned int)(X))	 


#define	HEAD_CODE_FA_EN				1	           //1 = head code is FA enable;0 = head code is FA disable
#define	TestUart1Rev_EN				0
#define	INT_EX1_EN					1




#define	MAX_UART_DATA_LEN		33	

#define D_LED_On  0						//low level enable,led is on
#define D_LED_Off  1						//high level enable,led is off

#define D_UpOrDown_Pressed		1
#define D_UpOrDown_Released		2

#define TRUE					1
#define FALSE					0

