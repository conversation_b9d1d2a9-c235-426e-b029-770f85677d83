/*******************************************************************************
 * 文件名: uart_test.c
 * 描述: SH79F084B UART简单测试程序 - 发送0x55
 * 作者: Assistant
 * 日期: 2025-01-03
 * 
 * 功能: 
 * 1. 初始化UART（9600波特率，8位数据，无奇偶校验，1位停止位）
 * 2. 循环发送0x55字节
 * 
 * 硬件连接:
 * - P1.2: TXD (发送)
 * - P1.3: RXD (接收)
 *******************************************************************************/

#include <SH79F084B.H>  // 使用SH79F083B头文件（兼容SH79F084B）

// 二进制宏定义
#define LongToBin(n) \
    (                    \
    ((n >> 21) & 0x80) | \
    ((n >> 18) & 0x40) | \
    ((n >> 15) & 0x20) | \
    ((n >> 12) & 0x10) | \
    ((n >>  9) & 0x08) | \
    ((n >>  6) & 0x04) | \
    ((n >>  3) & 0x02) | \
    ((n      ) & 0x01)   \
    )
#define bin(n) LongToBin(0x##n##l)

// 系统时钟频率定义
#define FSYSCLK 11059200UL  // 11.0592MHz晶振

// 波特率计算宏（使用定时器2）
#define CAL_UART_BAUDRATE(X) (65536-FSYSCLK/16/(X))

/*******************************************************************************
 * 函数名: DelayMs
 * 描述: 毫秒延时函数
 * 参数: ms - 延时毫秒数
 *******************************************************************************/
void DelayMs(unsigned int ms)
{
    unsigned int i, j;
    for(i = 0; i < ms; i++)
        for(j = 0; j < 1000; j++);
}

/*******************************************************************************
 * 函数名: SystemInit
 * 描述: 系统初始化
 *******************************************************************************/
void SystemInit(void)
{
    // 关闭所有中断
    EA = 0;
    
    // 配置GPIO
    P1 = bin(11000000);         // P1初始化
    P3 = bin(00000000);         // P3初始化
    
    // 配置P1.2(TXD)和P1.3(RXD)为准双向模式
    P1M0 = bin(00000000);       // P1.2、P1.3设为准双向模式
    P1M1 = bin(00000000);       // 00:准双向模式 01:推挽 10:输入 11:开漏输出
    
    // 配置系统时钟
    CLKCON = bin(00000000);     // 系统时钟不分频
    
    // 清看门狗
    RSTSTAT &= 0xF8;
}

/*******************************************************************************
 * 函数名: UartInit
 * 描述: UART初始化
 * 参数: baudrate - 波特率
 *******************************************************************************/
void UartInit(unsigned int baudrate)
{
    unsigned int iBaudRate;
    
    // 计算波特率重载值
    iBaudRate = CAL_UART_BAUDRATE(baudrate);
    
    // 配置定时器2作为波特率发生器
    TL2 = iBaudRate & 0xFF;         // 定时器2低字节
    TH2 = (iBaudRate >> 8) & 0xFF;  // 定时器2高字节
    RCAP2L = iBaudRate & 0xFF;      // 重载值低字节
    RCAP2H = (iBaudRate >> 8) & 0xFF; // 重载值高字节
    
    T2CON = bin(00110000);          // 定时器2用作UART时钟源，自动重载
    T2MOD = bin(10000000);          // 系统时钟作为定时器2时钟源
    TR2 = 1;                        // 启动定时器2
    
    // 配置UART
    SADDR = 0;                      // 从机地址
    SADEN = 0;                      // 从机地址掩码
    PCON &= bin(01111111);          // 清除SMOD位
    
    // 配置SCON寄存器
    // 模式1: 8位数据，可变波特率，无奇偶校验
    SCON = bin(01010000);           // SM0=0, SM1=1, REN=1 (允许接收)
    
    // 清除中断标志
    TI = 0;                         // 清除发送中断标志
    RI = 0;                         // 清除接收中断标志
}

/*******************************************************************************
 * 函数名: UartSendByte
 * 描述: 发送一个字节
 * 参数: dat - 要发送的数据
 *******************************************************************************/
void UartSendByte(unsigned char dat)
{
    SBUF = dat;                     // 将数据写入发送缓冲区
    while(TI == 0);                 // 等待发送完成
    TI = 0;                         // 清除发送完成标志
}

/*******************************************************************************
 * 函数名: UartSendString
 * 描述: 发送字符串
 * 参数: str - 要发送的字符串
 *******************************************************************************/
void UartSendString(unsigned char *str)
{
    while(*str)
    {
        UartSendByte(*str++);
    }
}

/*******************************************************************************
 * 函数名: main
 * 描述: 主函数
 *******************************************************************************/
void main(void)
{
    unsigned int counter = 0;
    
    // 系统初始化
    SystemInit();
    
    // UART初始化 - 9600波特率
    UartInit(9600);
    
    // 发送启动信息
    UartSendString("SH79F084B UART Test Start\r\n");
    
    // 主循环
    while(1)
    {
        // 清看门狗
        RSTSTAT &= 0xF8;
        
        // 发送0x55
        UartSendByte(0x55);
        
        // 每发送100次0x55后，发送一次计数信息
        counter++;
        if(counter >= 100)
        {
            counter = 0;
            UartSendString("\r\nSent 100 bytes of 0x55\r\n");
        }
        
        // 延时100ms
        DelayMs(100);
    }
}

/*******************************************************************************
 * 说明:
 * 
 * 1. 硬件连接:
 *    - P1.2 连接到串口调试工具的RX
 *    - P1.3 连接到串口调试工具的TX (可选，本程序只发送)
 *    - GND 连接到串口调试工具的GND
 * 
 * 2. 串口调试工具设置:
 *    - 波特率: 9600
 *    - 数据位: 8
 *    - 停止位: 1
 *    - 奇偶校验: 无
 *    - 流控: 无
 * 
 * 3. 预期现象:
 *    - 串口调试工具会收到连续的0x55字节
 *    - 每发送100个0x55后会收到提示信息
 * 
 * 4. 如果需要修改波特率，只需修改UartInit(9600)中的参数
 *    支持的波特率: 1200, 2400, 4800, 9600, 19200, 38400等
 *******************************************************************************/
